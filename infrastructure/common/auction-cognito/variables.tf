variable "project_name" {
  description = "Project name"
  type        = string
}

variable "environment" {
  description = "Environment (dev, staging, prod)"
  type        = string
}

variable "domain_prefix" {
  description = "Cognito domain prefix"
  type        = string
}

variable "password_policy" {
  description = "Password policy for the user pool"
  type = object({
    minimum_length                   = number
    require_lowercase                = bool
    require_numbers                  = bool
    require_symbols                  = bool
    require_uppercase                = bool
    temporary_password_validity_days = number
  })
  default = {
    minimum_length                   = 8
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = false
    require_uppercase                = true
    temporary_password_validity_days = 7
  }
}

variable "tenant_ids" {
  description = "List of tenant IDs for auction users"
  type        = list(string)
  default     = []
}

variable "callback_urls" {
  description = "List of callback URLs for the auction app client"
  type        = list(string)
  default     = []
}

variable "logout_urls" {
  description = "List of logout URLs for the auction app client"
  type        = list(string)
  default     = []
}
