// TODO： this api will remove, future will use this API: infrastructure/common/auction-side/gateway-resource/request-member/source/index.js

// COMMENTED OUT: Database-related imports (DB is offline for testing)
// const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`)
// const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`)
// const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`)
const Common = require(`${process.env.COMMON_LAYER_PATH}common.js`)

const {
  AdminAddUserToGroupCommand,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
  AdminUpdateUserAttributesCommand,
  CognitoIdentityProviderClient,
} = require('@aws-sdk/client-cognito-identity-provider')

// COMMENTED OUT: Database pool
// const pool = new PgPool()
const cognitoClient = new CognitoIdentityProviderClient({})

/**
 * Validates user input and checks for existing email
 * DATABASE TEMPORARILY DISABLED FOR TESTING
 */
async function validateInput(params, tenant) {
  const registerData = params.registerData

  // TODO: Database check for duplicate email
  // if (registerData?.email) {
  //   const members = await pool.query('SELECT * FROM "f_get_member_by_email"($1,$2);', [
  //     tenant.tenant_no,
  //     registerData.email,
  //   ]);
  //
  //   if (members && members.length > 0) {
  //     throw {
  //       status: 400,
  //       name: 'Email Already Exists',
  //       message: 'このメールアドレスは既に登録されています。',
  //     };
  //   }
  // }

  // Basic validation without database
  if (!registerData?.email) {
    throw {
      status: 400,
      name: 'Validation Error',
      message: 'メールアドレスが必要です。',
    }
  }

  if (!registerData?.memberName) {
    throw {
      status: 400,
      name: 'Validation Error',
      message: '会員名が必要です。',
    }
  }

  // Validate password confirmation
  if (registerData.password !== registerData.passwordConfirm) {
    throw {
      status: 400,
      name: 'Password Mismatch',
      message: 'パスワードと確認用パスワードが一致しません。',
    }
  }
}

/**
 * Creates a new user in the Cognito user pool
 */
async function createCognitoUser(email, password, memberData, tenantNo) {
  const command = new AdminCreateUserCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: email,
    UserAttributes: [
      {Name: 'email', Value: email},
      {Name: 'email_verified', Value: 'true'},
      {Name: 'custom:member_name', Value: memberData.memberName || ''},
      {Name: 'custom:language_code', Value: memberData.language || 'ja'},
    ],
    TemporaryPassword: password,
    MessageAction: 'SUPPRESS',
  })

  const createUserResult = await cognitoClient.send(command)
  console.log('User created in Cognito:', createUserResult)
  return createUserResult
}

/**
 * Sets the user's password to permanent in Cognito
 */
async function setCognitoPassword(username, password) {
  const command = new AdminSetUserPasswordCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: username,
    Password: password,
    Permanent: true,
  })
  await cognitoClient.send(command)
  console.log('Password set to permanent for user:', username)
}

/**
 * Adds the Cognito user to the tenant-specific group
 */
async function addUserToCognitoGroup(username, tenantNo) {
  const groupName = `tenant-id:${tenantNo}`
  const command = new AdminAddUserToGroupCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: username,
    GroupName: groupName,
  })
  await cognitoClient.send(command)
  console.log(`User ${username} added to group ${groupName}`)
  return groupName
}

/**
 * Creates member record in database
 * DATABASE TEMPORARILY DISABLED FOR TESTING
 */
async function createMemberInDb(memberData, tenantNo, password) {
  // COMMENTED OUT: Database member creation (DB is offline)
  // const freeField = {
  //   memberName: memberData.memberName,
  //   language: memberData.language || 'ja',
  //   email: memberData.email,
  //   // Add other member fields as needed
  // }
  //
  // const result = await pool.query(
  //   'SELECT * FROM "f_create_member_with_cognito"($1,$2,$3);',
  //   [tenantNo, JSON.stringify(freeField), memberData.email]
  // )
  //
  // console.log('Member created in database:', result)
  // return result[0]

  // Return mock data for testing without database
  console.log('TESTING MODE: Returning mock member data (database disabled)')
  return {
    member_no: Math.floor(Math.random() * 900) + 100, // Random number from 100 to 999
    user_no: Math.floor(Math.random() * 900) + 100,
    email: memberData.email,
    member_name: memberData.memberName,
    language_code: memberData.language || 'ja',
  }
}

/**
 * Updates Cognito user with member_no and user_no
 */
async function updateCognitoUserAttributes(username, memberNo, userNo) {
  const command = new AdminUpdateUserAttributesCommand({
    UserPoolId: process.env.AUCTION_COGNITO_USER_POOL_ID,
    Username: username,
    UserAttributes: [
      {Name: 'custom:member_no', Value: memberNo.toString()},
      {Name: 'custom:user_no', Value: userNo.toString()},
    ],
  })
  await cognitoClient.send(command)
  console.log(
    `Updated Cognito user ${username} with member_no: ${memberNo}, user_no: ${userNo}`
  )
}

/**
 * Main registration logic
 * DATABASE TEMPORARILY DISABLED FOR TESTING
 */
async function registerMember(e) {
  // TODO: use from common
  const params = Common.parseRequestBody(e.body)
  const header = e.headers

  console.log('TESTING MODE: Database operations disabled')
  console.log('Registration request params:', JSON.stringify(params, null, 2))

  // COMMENTED OUT: Database-dependent base initialization
  // const base = new Base(pool, params.languageCode)
  // await base.startRequest(e)

  // COMMENTED OUT: Database-dependent tenant check
  // const tenant = await base.checkOrigin(header.origin || header.Origin)
  // console.log('tenant:', tenant)

  // Mock tenant data for testing
  const tenant = {
    tenant_no: 1, // Mock tenant number for testing
    tenant_name: 'test-tenant',
  }
  console.log('TESTING MODE: Using mock tenant:', tenant)

  // Validate input (basic validation only, no database checks)
  await validateInput(params, tenant)

  const registerData = params.registerData
  const email = registerData.email
  const password = registerData.password

  let createdMember = null
  let cognitoUsername = null

  try {
    console.log('TESTING MODE: Starting Cognito-only registration flow')

    // 1. Create Cognito user
    console.log('Step 1: Creating Cognito user...')
    const createUserResult = await createCognitoUser(
      email,
      password,
      registerData,
      tenant.tenant_no
    )
    cognitoUsername = createUserResult.User.Username
    console.log('✓ Cognito user created successfully')

    // 2. Set permanent password
    console.log('Step 2: Setting permanent password...')
    await setCognitoPassword(cognitoUsername, password)
    console.log('✓ Password set to permanent')

    // 3. Add to tenant group
    console.log('Step 3: Adding user to tenant group...')
    const groupName = await addUserToCognitoGroup(
      cognitoUsername,
      tenant.tenant_no
    )
    console.log('✓ User added to tenant group')

    // 4. Create member record in database (returns mock data)
    console.log('Step 4: Creating member record (mock data)...')
    createdMember = await createMemberInDb(
      registerData,
      tenant.tenant_no,
      password
    )
    console.log('✓ Mock member data created')

    // 5. Update Cognito user with member_no and user_no
    console.log('Step 5: Updating Cognito user attributes...')
    await updateCognitoUserAttributes(
      cognitoUsername,
      createdMember.member_no,
      createdMember.user_no
    )
    console.log('✓ Cognito user attributes updated')

    const result = {
      message: 'Member registered successfully (TESTING MODE - DB disabled)',
      memberNo: createdMember.member_no,
      email: email,
      tenantId: tenant.tenant_no,
      groupName: groupName,
      testingMode: true,
    }

    console.log('TESTING MODE: Registration completed successfully:', result)
    return result
  } catch (error) {
    console.error('TESTING MODE: Registration error:', error)
    throw error
  }
}

/**
 * Lambda handler function
 * DATABASE TEMPORARILY DISABLED FOR TESTING
 */
exports.handle = (e, ctx, cb) => {
  ctx.callbackWaitsForEmptyEventLoop = false
  console.log('cognito-register-member (TESTING MODE):', e)

  registerMember(e)
    .then(result => {
      console.log('TESTING MODE: Registration successful, returning response')
      cb(null, {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Allow-Methods': 'OPTIONS,POST,GET',
        },
        body: JSON.stringify(result),
      })
    })
    .catch(error => {
      console.error('TESTING MODE: Registration failed:', error)
      const errorResponse = {
        status: error.status || 500,
        name: error.name || 'Registration Error',
        message: error.message || '登録に失敗しました。',
        testingMode: true,
      }
      cb(null, {
        statusCode: error.status || 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Headers': 'Content-Type',
          'Access-Control-Allow-Methods': 'OPTIONS,POST,GET',
        },
        body: JSON.stringify(errorResponse),
      })
    })
}
