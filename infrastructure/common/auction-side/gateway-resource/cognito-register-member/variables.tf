variable "project_name" {
  description = "project_name"
}

variable "environment" {
  description = "environment"
}

variable "allow_origin" {
  description = "allow_origin"
}

variable "s3-bucket-arn" {
  description = "s3-bucket-arn"
}

variable "s3-bucket-id" {
  description = "s3-bucket-id"
}

variable "aws_api_gateway_rest_api_gateway_id" {
  description = "aws_api_gateway_rest_api_gateway_id"
}

variable "parent_id" {
  description = "parent_id"
}

variable "parent_path" {
  description = "parent_path"
  default = ""
}

variable "prefix_function_name" {
  description = "prefix_function_name"
  default     = "auction"
}

variable "lambda_subnet_ids" {
  description = "lambda_subnet_ids"
  default     = []
}

variable "lambda_security_group_id" {
  description = "lambda_security_group_id"
  default     = ""
}

variable "lambda_global_environment_variables" {
  description = "lambda_global_environment_variables"
  default     = {}
}

variable "lambda_layer" {
  description = "lambda_layer"
  default     = []
}

variable "slack-notification-lambda-arn" {
  description = "slack-notification-lambda-arn"
}

variable "aws_api_gateway_rest_api_gateway_execution_arn" {
  description = "aws_api_gateway_rest_api_gateway_execution_arn"
}

variable "authorization" {
  description = "authorization"
  default     = "NONE"
}

variable "aws_api_gateway_authorizer_id" {
  description = "aws_api_gateway_authorizer_id"
  default     = ""
}

variable "cognito_user_pool_id" {
  description = "ID of cognito user pool, used to validate JWT tokens"
  type        = string
}

variable "cognito_client_id" {
  description = "ID of Cognito App Client"
  type        = string
}
