const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.READ_ONLY_PGHOST);

exports.handle = function (e, ctx, cb) {
  console.log('log of event : ', e);
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  ctx.callbackWaitsForEmptyEventLoop = false;
  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      const sqlParams = [params.field_no, tenantNo, params.language_code];
      return pool
        .rlsQuery(tenantNo, Define.QUERY.CHECK_FIELD_ITEM_USED, sqlParams)
        .then(checkResult => {
          const isExists =
            checkResult[0] && checkResult[0].is_exists
              ? checkResult[0].is_exists
              : false;
          if (isExists) {
            const error = {
              status: 409,
              errors: {
                field_item_used: Define.MESSAGE.E000368,
              },
            };
            throw error;
          }
          const ret = {
            is_used: isExists,
          };
          return Promise.resolve(ret);
        });
    })
    .then(res => {
      return Base.createSuccessResponse(cb, res);
    })
    .catch(error => Base.createErrorResponse(cb, error));
};
