const Validator = require(`${process.env.COMMON_LAYER_PATH}validator.js`);
const Base = require(`${process.env.COMMON_LAYER_PATH}base.js`);
const Define = require(`${process.env.COMMON_LAYER_PATH}define.js`);
const PgPool = require(`${process.env.COMMON_LAYER_PATH}pg-pool.js`);
const pool = new PgPool(process.env.PGHOST);

exports.handle = function (e, ctx, cb) {
  const params = Base.parseRequestBody(e.body);
  const tenantNo = Base.extractTenantId(e);
  const admin_no = Base.extractAdminNo(e);
  ctx.callbackWaitsForEmptyEventLoop = false;
  console.log(params);

  Promise.resolve()
    .then(() => Base.startRequest(e))
    .then(() =>
      Base.checkAccessIpAddress(
        pool,
        e.requestContext.identity.sourceIp,
        tenantNo
      )
    )
    .then(() => {
      // Validation
      if (params.chat_message.length > 300) {
        const error = {
          status  : 400,
          message : Define.MESSAGE.E000804
        }
        return Promise.reject(error)
      } else {
        return Promise.resolve()
      }
    })
    .then(() => {
      const sqlParams = [
        tenantNo,
        params.exhibition_message_no,
        admin_no
      ]
      return pool.rlsQuery(tenantNo, Define.QUERY.UPDATE_INQUIRY_CHAT_FUNCTION, sqlParams)
    })
    .then(() => {
      const sqlParams = [
        tenantNo,
        params.exhibition_item_no,
        params.exhibition_message_no,
        params.chat_message,
        admin_no
      ]
      return pool.rlsQuery(tenantNo, Define.QUERY.REGIST_INQUIRY_CHAT_FUNCTION, sqlParams)
    })
    .then(() => {
      const sqlParams = [
        tenantNo,
        '3', // お問い合わせチャット
        params.exhibition_message_no,
        params.exhibition_item_no,
        null,
        admin_no
      ]
      return pool.rlsQuery(tenantNo, Define.QUERY.INSERT_INQUIRY_CHAT_NOTIFICATION, sqlParams)
    })
    // TODO メール送信
    .then(result => {
      return Base.createSuccessResponse(cb, result);
    })
    .catch(error => {
      return Base.createErrorResponse(cb, error);
    });
};
