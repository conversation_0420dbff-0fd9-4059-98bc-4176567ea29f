<template>
  <div>
    <CCard v-for="lang in languageOptions">
      <CCardHeader>
        <strong>{{ lang.label }}</strong>
      </CCardHeader>
      <CCardBody>
        <InputFormBody
          :memberEditData="memberEditData"
          :lang="lang.value"
          :confirmMode="isReadOnly || confirmMode"
        />
        <CElementCover v-if="loading" :opacity="0.8" />
      </CCardBody>
    </CCard>
    <CCard>
      <CommonForm
        :memberEditData="memberEditData"
        :confirmMode="isReadOnly || confirmMode"
      />
      <CCardFooter>
        <CButton class="mx-1" color="light" @click="backButton">
          {{ confirmMode ? '編集画面に戻る' : '編集を中止して一覧に戻る' }}
        </CButton>
        <CButton
          v-if="!isReadOnly"
          class="mx-1"
          color="primary"
          @click="validOrCreateMember"
        >
          {{ confirmMode ? '更新する' : '確認画面へ' }}
        </CButton>
        <CElementCover v-if="loading" :opacity="0.8" />
      </CCardFooter>
    </CCard>

    <!-- Modal show when validating-->
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="validateModal"
      @close="
        () => {
          validateModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{ validateModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          v-if="validateResult.length > 0"
          @click="validateModal = false"
          color="dark"
          :disabled="loading"
          >閉じる
        </CButton>
      </CModalFooter>
    </CModal>

    <!-- Modal use for update confirm -->
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="updateModal"
      @close="
        () => {
          updateModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>{{ updateModalTitle }}</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="!loading && validateResult.length === 0">
          この内容で更新してもよろしいですか？
        </div>
        <div style="margin-left: 10px">
          <scale-loader
            :loading="loading"
            color="#5dc596"
            height="10px"
            width="4px"
          ></scale-loader>
        </div>
        <div v-if="validateResult">
          <div v-for="(val, i) in validateResult" :key="i">{{ val }}</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <CButton
          v-if="validateResult.length === 0"
          @click="updateModal = false"
          color="dark"
          :disabled="loading"
          >キャンセル
        </CButton>
        <CButton @click="handleUpdateMember" color="primary" :disabled="loading"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <!-- Modal use for cancel confirm -->
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>更新中止確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="cancelModal = false" color="dark">キャンセル</CButton>
        <CButton
          @click="
            () => {
              cancelModal = false;
              nextPage();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <!-- Modal use for success -->
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="successModal"
      @close="
        () => {
          successModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="goToMemberList" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>

    <!-- Modal use for fail -->
    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="failModal"
      @close="
        () => {
          failModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>失敗</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>更新エラーが発生しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="failModal = false" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
import Base from '@/common/base';
import {CElementCover, ScaleLoader} from '@/components/Table';
import {useAuthStore} from '@/store/auth';
import cloneDeep from 'lodash-es/cloneDeep';
import {computed, onMounted, ref} from 'vue';
import {onBeforeRouteLeave, useRoute, useRouter} from 'vue-router';
import CommonForm from '../new/CommonForm.vue';
import InputFormBody from '../new/InputFormBody.vue';
  const router = useRouter();
  const route = useRoute();
  const {isReadOnly} = useAuthStore();

  const loading = ref(true);
  // Check change
  const memberOrig = ref(null);
  const forceExit = ref(false);
  // Confirm mode
  const confirmMode = ref(false);
  const memberEditData = ref({
    bidAllowFlag: null,
    emailDeliveryFlag: null,
    freeField: {
    },
  });
  const updateModal = ref(false);
  const cancelModal = ref(false);
  const successModal = ref(false);
  const failModal = ref(false);
  const nextPage = ref(null);
  // Update result
  const validateResult = ref([]);
  const updateModalTitle = ref('確認');
  // Validate input
  const validateModal = ref(false);
  const validateModalTitle = ref('確認');
  // Constant
  const constants = ref([]);
  // Check valid date time
  const dateTimeValid = ref({
    establishmentDate: true,
    ceoBirthday: true,
    antiquePermitDate: true,
  });

  const countryOptions = computed(() => {
    const countries =
      constants.value?.filter(x => x.key_string === 'COUNTRY_CODE') || [];
    return countries.map(c => {
      return {
        value: c.value1,
        label: c.value2,
      };
    });
  });
  const emailLangList = [
    {title: 'EN', value: 'en'},
    {title: 'JA', value: 'ja'},
  ];

  const constLanguageOptions = ref([])
  const languageOptions = ref([])

  const getConstantsData = () => {
    loading.value = true;
    const params = {
      key_strings: ['COUNTRY_CODE', 'MEMBER_STATUS_CODE', 'LANGUAGE_CODE'],
    };
    return Methods.apiExecute('get-constants-by-keys', params).then(
      response => {
        if (response.status === 200) {
          const data = response.data;
          constants.value = data || [];
          constLanguageOptions.value = [];
          for (const constant of response.data) {
            switch (constant.key_string) {
              case 'LANGUAGE_CODE':
                constLanguageOptions.value.push({
                  value: constant.value1,
                  label: constant.value2,
                });
                break;
              default:
                break;
            }
          }
        }
        loading.value = false;
        return Promise.resolve();
      }
    );
  };

  const getMemberData = async () => {
    memberOrig.value = null;
    // Request to server
    await Methods.apiExecute('get-member', {
      member_request_no: route.params.id,
    })
      .then(response => {
        loading.value = false;
        const member =
          response.data && response.data.length > 0 ? response.data[0] : null;
        if (member === null) {
          return Promise.resolve(null);
        }
        // Set member data
        memberEditData.value = {
          memberNo: member.member_no,
          memberRequestNo: member.member_request_no,
          memberId: member.member_id,
          status: member.status,
          exhibitionAllowFlag: member.exhibition_allow_flag,
          bidAllowFlag: member.bid_allow_flag
            ? String(member.bid_allow_flag)
            : '0',
          emailDeliveryFlag: member.email_delivery_flag
            ? String(member.email_delivery_flag)
            : '0',
          freeField: member.free_field,
        };
        // Set original data
        memberOrig.value = cloneDeep(memberEditData.value);
        return Promise.resolve();
      })
      .catch(error => {
        loading.value = false;
        if (
          !(
            error.response &&
            (error.response.status === 400 || error.response.status === 409)
          )
        ) {
          forceExit.value = true;
        }
        Methods.parseHtmlResponseError(router, error);
      });
  };

  const backButton = () => {
    if (confirmMode.value) {
      // Back to member edit mode
      confirmMode.value = false;
      Base.scrollToTop();
    } else {
      // Back to member list
      router.push({path: '/members'});
    }
  };

  const openCreateModal = () => {
    console.log('openCreateModal');
    updateModalTitle.value = '確認';
    updateModal.value = true;
    loading.value = false;
    validateResult.value = [];
  };

  const validate = async member => {
    validateResult.value = [];

    const params = {
      member,
      validation_mode: true,
    };

    try {
      await Methods.apiExecute('update-member', params);
    } catch (error) {
      if (
        !(
          error.response &&
          (error.response.status === 400 || error.response.status === 409)
        )
      ) {
        forceExit.value = true;
      }
      validateResult.value = Methods.parseHtmlResponseError(router, error);
    }
  };

  const validateMember = async () => {
    console.log('validateMember');

    validateModal.value = true;
    validateModalTitle.value = '確認';
    validateResult.value = [];
    confirmMode.value = false;
    loading.value = true;

    // Email delivery flag
    const member = JSON.parse(JSON.stringify(memberEditData.value));
    member.emailDeliveryFlag = Number(member.emailDeliveryFlag) ? 1 : 0;
    member.bidAllowFlag = member.bidAllowFlag ? Number(member.bidAllowFlag) : 0;

    // Validation
    await validate(member);

    // エラーがない場合はデータ更新へ進める
    if (validateResult.value.length === 0) {
      // Go to member confirm screen
      confirmMode.value = true;
      loading.value = false;
      validateModal.value = false;
      Base.scrollToTop();
    } else {
      loading.value = false;
      validateModalTitle.value = '入力エラー';
    }
  };

  const validOrCreateMember = () => {
    if (confirmMode.value === true) {
      // 入力確認を実行した後memberを更新
      openCreateModal();
      loading.value = false;
    } else {
      // 未確認なので入力確認を行う
      validateMember();
    }
  };

  const createMemberApi = async member => {
    const params = {
      member,
    };
    console.log(member);
    await Methods.apiExecute('update-member', params)
      .then(response => {
        console.log(`response: ${JSON.stringify(response)}`);
        updateModal.value = false;
        loading.value = false;
        if (response.status === 200) {
          successModal.value = true;
        } else {
          failModal.value = true;
        }
      })
      .catch(error => {
        loading.value = false;
        updateModal.value = false;
        if (
          error.response &&
          (error.response.status === 400 || error.response.status === 409)
        ) {
          failModal.value = true;
        } else {
          forceExit.value = true;
        }
        Methods.parseHtmlResponseError(router, error);
      });
  };

  const handleUpdateMember = async () => {
    if (validateResult.value.length > 0) {
      updateModal.value = false;
      return;
    }
    loading.value = true;

    // Email delivery flag
    const member = Object.assign({}, memberEditData.value, {
      emailDeliveryFlag: Number(memberEditData.value.emailDeliveryFlag) ? 1 : 0,
      bidAllowFlag: memberEditData.value.bidAllowFlag
        ? Number(memberEditData.value.bidAllowFlag)
        : 0,
    });

    // Validation
    await validate(member);

    // エラーがない場合はデータ更新へ進める
    if (validateResult.value.length === 0) {
      // Update member data to DB
      await createMemberApi(member);
    } else {
      loading.value = false;
      updateModalTitle.value = '入力エラー';
    }
  };

  const goToMemberList = event => {
    event.target.disabled = true;
    successModal.value = false;
    forceExit.value = true;
    router.push({path: '/members'});
  };

  onMounted(async () => {
    loading.value = true;
    await getConstantsData();
    await getMemberData();
    loading.value = false;

    const response = await Methods.apiExecute('get-tenant-language-list', {})
    languageOptions.value = response.data.language_code_list.map(
      cd => {
        const lang_list = constLanguageOptions.value.find(
          item => item.value === cd
        );
        return {
          value: cd,
          label: lang_list ? lang_list.label : '',
        };
      }
    )

    if (isReadOnly) {
      // 一般の管理者の場合は[/dashboard]ページに遷移する
      router.push({path: '/dashboard'});
    }
  });

  onBeforeRouteLeave((to, from, next) => {
    if (
      !forceExit.value &&
      !Base.objectsAreIdentical(memberOrig.value, memberEditData.value)
    ) {
      nextPage.value = next;
      cancelModal.value = true;
    } else {
      // eslint-disable-next-line callback-return
      next();
    }
  });
</script>
