<script setup>
import Methods from '@/api/methods';
import {computed, defineProps, onMounted, ref} from 'vue';
import FileSelection from './fileSelection.vue';

  const props = defineProps({
    memberEditData: {
      type: Object,
      default: {},
    },
    lang: {
      type: String,
      default: '',
    },
    confirmMode: {
      type: Boolean,
      default: false,
    }
  });

  let fieldList = ref([])
  let constants = ref({})
  const confirmMode = computed(() => props.confirmMode)
  onMounted(async () => {
    const response = await Methods.apiExecute('get-field-list', {
      language_code: props.lang,
      resource_type: "member"
    })
    fieldList.value = response.data.data
    const constantsRes = await Methods.apiExecute('get-constants-by-keys', {
      key_strings: fieldList.value.filter(row => row.input_data_list && row.input_data_list.key_string).map(row => row.input_data_list.key_string),
    })
    constantsRes.data.forEach(element => {
      if (!constants.value[element.key_string])
      constants.value[element.key_string] = [{
        value: null,
        label: "",
      }]
      constants.value[element.key_string].push({
        value: element.value1,
        label: element.value2,
      })
    });
  });
</script>

<template>
  <CForm onsubmit="return false;" v-if="confirmMode">
    <CRow class="mb-3" v-for="field in fieldList">
      <CCol sm="2">
        <label>{{ field.logical_name }}</label>
      </CCol>
      <CCol sm="4">
        <CFormSelect
        v-if="field.input_type == 'pulldown'"
        :name="field.physical_name"
        :options="constants[field.input_data_list.key_string]"
        v-model="memberEditData.freeField[field.physical_name]"
        disabled
      />
      <label v-else-if="field.input_type == 'file' && memberEditData.freeField[field.physical_name]" class="word-break">
        <span v-for="file in memberEditData.freeField[field.physical_name]">{{ file }}　</span>
      </label>
      <label v-else class="word-break">{{
        memberEditData.freeField[field.physical_name]
      }}</label>
      </CCol>
    </CRow>
  </CForm>
  <CForm onsubmit="return false;" v-else>
    <CRow class="mb-3" v-for="field in fieldList">
      <CCol sm="3" class="d-flex align-items-start" style="line-height: 40px;">
        <label>{{ field.logical_name }}</label>
        <CBadge v-if="field.required_flag" color="danger" class="ms-auto" style="margin: 10px 0;">必須</CBadge>
      </CCol>
      <CCol sm="4">
        <CFormInput
          v-if="field.input_type == 'text'"
          :name="field.physical_name"
          :maxlength="field.max_length"
          v-model="memberEditData.freeField[field.physical_name]"
        />
        <CFormSelect
          v-if="field.input_type == 'pulldown'"
          :name="field.physical_name"
          :options="constants[field.input_data_list.key_string]"
          v-model="memberEditData.freeField[field.physical_name]"
        />
        <CFormCheck
          v-if="field.input_type == 'checkbox'"
          v-model="memberEditData.freeField[field.physical_name]"
          :name="field.physical_name"
        />
        <FileSelection
          v-if="field.input_type == 'file'"
          @onFilesChange="memberEditData.freeField[field.physical_name] = $event.map(row => row.fileName)"
        />
      </CCol>
    </CRow>
  </CForm>
</template>
