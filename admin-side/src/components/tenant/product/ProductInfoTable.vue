<template>
  <CCard>
    <CCardHeader class="form-inline" style="display: block">
      <slot name="header">
        <CIcon name="cil-grid" />{{ caption }}
        <span style="float: right">総件数: {{ current_count }}件</span>
      </slot>
    </CCardHeader>
    <CCardBody>
      <CDataTable
        hover
        striped
        border
        small
        fixed
        sorter
        :loading="loading"
        :items="items"
        :fields="fields"
      >
        <template #logical_name="{item}">
          <td class="text-left" style="width: 200px">
            {{ item.logical_name }}
          </td>
        </template>
        <template #physical_name="{item}">
          <td class="text-left" style="width: 150px">
            {{ item.physical_name }}
          </td>
        </template>
        <template #input_type="{item}">
          <td class="text-left" style="width: 150px">
            <CFormSelect
              name="input_type"
              :options="inputMethodOptions"
              v-model="item.input_type"
              disabled
            />
          </td>
        </template>
        <template #required_flag="{item}">
          <td class="text-center" style="width: 100px">
            <CFormCheck :checked="item.required_flag === 1" disabled />
          </td>
        </template>
        <template #data_type="{item}">
          <td>
            <CFormSelect
              name="data_type"
              :options="inputValidationOptions"
              v-model="item.data_type"
              disabled
            />
          </td>
        </template>
        <template #restrictions="{item}">
          <td class="text-left" style="width: 200px">
            {{ getRestrictions(item) }}
          </td>
        </template>

        <template #action="{item}">
          <td style="width: 150px">
            <div class="d-flex justify-content-center align-items-center gap-1">
              <CButton size="sm" color="success" @click="editItem(item)"
                >編集</CButton
              >
              <CButton size="sm" color="danger" @click="deleteItem(item)"
                >削除</CButton
              >
            </div>
          </td>
        </template>
      </CDataTable>
    </CCardBody>

    <!-- Add new item -->
    <CCardHeader
      style="border-top: 1px solid; border-bottom: none; border-color: #d8dbe0"
    >
      <slot name="header"><strong>新規項目追加</strong></slot>
    </CCardHeader>
    <CCardBody>
      <AddNewItem
        ref="addNewItemRef"
        :parentInputValidationOptions="inputValidationOptions"
        :parentInputMethodOptions="inputMethodOptions"
        :parentConstantKey="props.parentConstantKey"
        @add-new-item="addNewItem"
      />
    </CCardBody>
  </CCard>
</template>

<script setup>
  import {CDataTable} from '@/components/Table';
  import {defineEmits, defineProps, defineExpose, ref} from 'vue';
  import {useRouter} from 'vue-router';
  import AddNewItem from './AddNewItem.vue';

  const emit = defineEmits([
    'page-change',
    'sorter-change',
    'pagination-change',
    'add-new-item',
    'delete-item',
    'edit-item',
  ]);
  const props = defineProps({
    items: Array,
    current_count: {
      type: String,
      default: '0',
    },
    total_count: {
      type: String,
      default: '0',
    },
    fields: {
      type: Array,
      default() {
        return [
          {
            key: 'logical_name',
            label: '項目名',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'physical_name',
            label: '物理名',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'input_type',
            label: '入力方法',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'required_flag',
            label: '必須',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'data_type',
            label: '入力制限',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'restrictions',
            label: '制限内容',
            _classes: 'text-center',
            sorter: false,
          },
          {
            key: 'action',
            label: 'アクション',
            _classes: 'text-center',
            sorter: false,
          },
        ];
      },
    },
    caption: {
      type: String,
      default: '',
    },
    loading: Boolean,
    parentConstantKey: Array,
  });

  const addNewItemRef = ref(null);

  const router = useRouter();

  const inputValidationOptions = ref([
    {value: 'constant', label: '定数'},
    {value: 'character', label: '文字'},
    {value: 'integer', label: '数値（小数なし）'},
    {value: 'float', label: '数値（小数あり）'},
    {value: 'reg', label: '正規表現'},
  ]);

  const inputMethodOptions = ref([
    {value: 'pulldown', label: 'プルダウン'},
    {value: 'text', label: 'テキスト'},
    {value: 'checkbox', label: 'チェックボックス'},
    {value: 'file', label: 'ファイル'},
  ]);

  // 削除用メソッド
  const deleteItem = item => {
    emit('delete-item', item);
  };

  // 編集用メソッド
  const editItem = item => {
    emit('edit-item', item);
  };

  // 新規項目追加メソッド
  const addNewItem = item => {
    console.log('addNewItem');
    emit('add-new-item', item);
  };

  // 子コンポーネントの入力値リセットのメソッド
  const resetItem = () => {
    console.log('ProductInfoTable resetItem');
    console.log('addNewItemRef:', addNewItemRef.value);
    if (addNewItemRef.value) {
      addNewItemRef.value.resetItem();
    } else {
      console.error('addNewItemRef is null');
    }
  };
  defineExpose({resetItem});

  // 入力制限内容の取得
  const getRestrictions = item => {
    let restrictions = '';
    if (item.data_type === 'constant') {
      restrictions = item.input_data_list
        ? item.input_data_list.key_string
        : '';
    } else if (item.data_type === 'character') {
      restrictions = item.max_length ? String(item.max_length) : '';
    } else if (item.data_type === 'integer' || item.data_type === 'float') {
      restrictions = item.max_value ? String(item.max_value) : '';
    } else if (item.data_type === 'reg') {
      restrictions = item.regular_expressions ? item.regular_expressions : '';
    } else {
      restrictions = '';
    }

    return restrictions;
  };
</script>
