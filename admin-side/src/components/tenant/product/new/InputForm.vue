<template>
  <div>
    <CCard>
      <CCardHeader>
        <strong>項目編集</strong>
      </CCardHeader>
      <CCardBody v-show="!isReadOnly">
        <InputFormBody
          :itemData="itemData"
          :inputMethodOptions="inputMethodOptions"
          :inputValidationOptions="inputValidationOptions"
          :constantKeyOptions="ConstantKeyOptions"
        />
        <CElementCover v-if="loading" :opacity="0.8" />
      </CCardBody>

      <div v-if="isReadOnly">
        <Confirm
          :itemData="itemData"
          :inputMethodOptions="inputMethodOptions"
          :inputValidationOptions="inputValidationOptions"
          :constantKeyOptions="ConstantKeyOptions"
        />
      </div>
      <CCardFooter>
        <CButton class="mx-1" color="light" @click="backButton">
          一覧に戻る
        </CButton>
        <CButton
          v-if="!isReadOnly"
          class="mx-1"
          color="primary"
          @click="confirmModal = true"
        >
          更新する
        </CButton>
        <CElementCover v-if="loading" :opacity="0.8" />
      </CCardFooter>
    </CCard>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="confirmModal"
      @close="
        () => {
          confirmModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-if="loading">
          <div style="margin-left: 10px">
            <scale-loader
              :loading="loading"
              color="#5dc596"
              height="10px"
              width="4px"
            ></scale-loader>
          </div>
        </div>
        <div v-else>
          <div v-if="errorMsg.length > 0">
            <div v-for="(val, i) in errorMsg" :key="i">{{ val }}</div>
          </div>
          <div v-else>この内容で更新してもよろしいですか？</div>
        </div>
      </CModalBody>
      <CModalFooter>
        <template v-if="errorMsg.length > 0">
          <CButton
            @click="
              confirmModal = false;
              errorMsg = [];
            "
            color="primary"
          >
            OK
          </CButton>
        </template>
        <template v-else>
          <CButton
            @click="confirmModal = false"
            color="dark"
            :disabled="loading"
            >キャンセル
          </CButton>
          <CButton
            @click="updateField(itemData)"
            color="primary"
            :disabled="loading"
            >OK</CButton
          >
        </template>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="cancelModal"
      @close="
        () => {
          cancelModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>更新中止確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>入力内容は破棄されますがよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="cancelModal = false" color="dark">キャンセル</CButton>
        <CButton
          @click="
            () => {
              cancelModal = false;
              nextPage();
            }
          "
          color="danger"
          >OK</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="successModal"
      @close="
        () => {
          successModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="goToMemberList" color="dark">閉じる</CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script setup>
  import Methods from '@/api/methods';
import Base from '@/common/base';
import {CElementCover, ScaleLoader} from '@/components/Table';
import {useAuthStore} from '@/store/auth';
import cloneDeep from 'lodash-es/cloneDeep';
import {onMounted, ref} from 'vue';
import {onBeforeRouteLeave, useRouter} from 'vue-router';
import Confirm from './Confirm.vue';
import InputFormBody from './InputFormBody.vue';

  const props = defineProps({
    field_localized_no: Number,
  });

  const router = useRouter();
  const {isReadOnly} = useAuthStore();
  const loading = ref(true);
  // Check change
  const OriginalItem = ref(null);
  const forceExit = ref(false);
  const itemData = ref({});

  const confirmModal = ref(false);
  const cancelModal = ref(false);
  const successModal = ref(false);
  const nextPage = ref(null);

  // 定数キー
  const ConstantKeyOptions = ref([]);

  // リクエストパラメータ
  const params = ref({});

  // error message
  const errorMsg = ref([]);

  // 入力制限
  const inputValidationOptions = [
    {value: 'constant', label: '定数'},
    {value: 'character', label: '文字'},
    {value: 'integer', label: '数値（小数なし）'},
    {value: 'float', label: '数値（小数あり）'},
    {value: 'reg', label: '正規表現'},
  ];
  // 入力方法
  const inputMethodOptions = [
    {value: 'pulldown', label: 'プルダウン'},
    {value: 'text', label: 'テキスト'},
    {value: 'checkbox', label: 'チェックボックス'},
    {value: 'file', label: 'ファイル'},
  ];

  const backButton = () => {
    router.push({path: '/tenant/product-info'});
  };

  const validateItem = () => {
    let validationFlag = true;
    if (!params.value.logical_name || params.value.logical_name.length === 0) {
      validationFlag = false;
      errorMsg.value.push('項目名は必須です。');
    }
    if (
      !params.value.physical_name ||
      params.value.physical_name.length === 0
    ) {
      validationFlag = false;
      errorMsg.value.push('物理名は必須です。');
    }
    if (!params.value.input_type || params.value.input_type.length === 0) {
      validationFlag = false;
      errorMsg.value.push('入力方法は必須です。');
    }
    if (!params.value.data_type || params.value.data_type.length === 0) {
      validationFlag = false;
      errorMsg.value.push('入力制限は必須です。');
    } else {
      if (
        params.value.data_type === 'constant' &&
        (!params.value.input_data_list ||
          params.value.input_data_list.length === 0)
      ) {
        validationFlag = false;
        errorMsg.value.push('定数キーは必須です。');
      }
    }

    if (validationFlag) {
      if (params.value.data_type === 'constant') {
        params.value.max_length = null;
        params.value.max_value = null;
        params.value.regular_expressions = null;
        // オブジェクト型に変換
        params.value.input_data_list = {
          key_string: params.value.input_data_list,
        };
      } else if (params.value.data_type === 'character') {
        params.value.input_data_list = null;
        params.value.max_value = null;
        params.value.regular_expressions = null;
      } else if (
        params.value.data_type === 'integer' ||
        params.value.data_type === 'float'
      ) {
        params.value.input_data_list = null;
        params.value.max_length = null;
        params.value.regular_expressions = null;
      } else if (params.value.data_type === 'reg') {
        params.value.input_data_list = null;
        params.value.max_length = null;
        params.value.max_value = null;
      }
    }

    return validationFlag;
  };

  const updateField = async item => {
    loading.value = true;
    errorMsg.value = [];

    params.value = {
      ...item,
    };
    // Booleanを0と1に変換
    params.value.required_flag = params.value.required_flag ? 1 : 0;

    // エラーチェック、不要なパラメータの削除
    const isValid = validateItem();

    if (!isValid) {
      loading.value = false;
    } else {
      console.log(params.value);
      await Methods.apiExecute('regist-field-item', params.value)
        .then(response => {
          confirmModal.value = false;
          loading.value = false;
          if (response.status === 200) {
            successModal.value = true;
          } else {
            errorMsg.value = Methods.parseHtmlResponseError(router, response);
            confirmModal.value = true;
          }
        })
        .catch(error => {
          loading.value = false;
          confirmModal.value = false;
          if (
            error.response &&
            (error.response.status === 400 || error.response.status === 409)
          ) {
          } else {
            forceExit.value = true;
          }
          confirmModal.value = true;
          errorMsg.value = Methods.parseHtmlResponseError(router, error);
        });
    }
  };

  const goToMemberList = event => {
    event.target.disabled = true;
    successModal.value = false;
    forceExit.value = true;
    router.push({path: '/tenant/product-info'});
  };

  const getFieldItem = async () => {
    return Methods.apiExecute('get-field-item', {
      field_localized_no: props.field_localized_no,
    })
      .then(response => {
        if (response.status === 200) {
          itemData.value = response.data.data;
          itemData.value.input_data_list = itemData.value.input_data_list
            ? itemData.value.input_data_list.key_string
            : '';
          // required_flagを数値からBooleanに変換
          itemData.value.required_flag = itemData.value.required_flag === 1;
          OriginalItem.value = cloneDeep(itemData.value);
          loading.value = false;
          if (itemData.value.field_no === undefined) {
            errorMsg.value.push('データがありません。');
            loading.value = false;
            confirmModal.value = true;
          }
        }
      })
      .catch(error => {
        forceExit.value = true;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
        confirmModal.value = true;
      });
  };

  const getConstantKey = async () => {
    const request = {};
    return Methods.apiExecute('get-constant-key', request)
      .then(response => {
        if (response.status === 200) {
          ConstantKeyOptions.value = [];
          ConstantKeyOptions.value.push({label: '', value: ''});
          response.data.map(constant => {
            ConstantKeyOptions.value.push({
              label: constant.key_string,
              value: constant.key_string,
            });
          });
        }
        return Promise.resolve();
      })
      .catch(error => {
        forceExit.value = true;
        errorMsg.value = Methods.parseHtmlResponseError(router, error);
        confirmModal.value = true;
      });
  };

  onMounted(async () => {
    loading.value = true;
    if (props.field_localized_no) {
      await getFieldItem();
      await getConstantKey();
    } else {
      errorMsg.value.push('不正なリクエストです。');
      loading.value = false;
      confirmModal.value = true;
    }
  });

  onBeforeRouteLeave((to, from, next) => {
    if (
      !forceExit.value &&
      !Base.objectsAreIdentical(OriginalItem.value, itemData.value)
    ) {
      nextPage.value = next;
      cancelModal.value = true;
    } else {
      // eslint-disable-next-line callback-return
      next();
    }
  });
</script>
