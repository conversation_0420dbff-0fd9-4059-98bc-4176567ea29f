import {PutObjectCommand, S3Client} from '@aws-sdk/client-s3';
import Methods from './methods';

export default {
  /*
   * * Type: updaload type. Ex: 'notice'
   *    File: file object
   *
   */
  upload(api_type, file, callback) {
    Promise.resolve()
      .then(() => {
        // Read content from the file
        return this.readFileAllBrowsers(file);
      })
      .then(content => {
        if (typeof content === 'undefined' || content === null) {
          const err = {
            status: 404,
            message: 'Read file failed!',
          };
          return Promise.reject(err);
        }
        return content;
      })
      .then(fileContent => {
        return this.bufferToS3(api_type, file.name, file.type, fileContent);
      })
      .then(ret => {
        return callback(ret);
      })
      .catch(error => {
        return callback(error);
      });
  },
  uploadPromise(api_type, file) {
    return Promise.resolve()
      .then(() => {
        // Read content from the file
        return this.readFileAllBrowsers(file).then(content => {
          if (typeof content === 'undefined' || content === null) {
            const err = {
              status: 404,
              message: 'Read file failed!',
            };
            return Promise.reject(err);
          }
          return Promise.resolve(content);
        });
      })
      .then(fileContent => {
        return this.bufferToS3(api_type, file.name, file.type, fileContent);
      });
  },
  bufferToS3(api_type, fileName, fileType, fileContent) {
    return Promise.resolve()
      .then(() => {
        // Setting up S3 upload parameters
        return Methods.apiExecute('get-aws-credentials', {
          type: api_type,
        });
      })
      .then(response => {
        console.log('response = ', response);
        if (response.status === 200) {
          // Get reference to S3 client
          const s3Info = response.data;
          const client = new S3Client({
            region: s3Info.region,
            credentials: {
              accessKeyId: s3Info.credentials.accessKeyId,
              secretAccessKey: s3Info.credentials.secretAccessKey,
              sessionToken: s3Info.credentials.sessionToken,
            },
          });
          const key = `${s3Info.prefix_key}/${fileName}`;
          const uploadParams = {
            Bucket: s3Info.bucket,
            Key: key,
            Body: fileContent,
            ContentType: fileType,
          };
          return client.send(new PutObjectCommand(uploadParams)).then(() => {
            return Promise.resolve({
              status: 200,
              message: key,
            });
          });
        }
        const err = {
          status: response.status,
          message: response.message,
        };
        return Promise.reject(err);
      });
  },
  // Get file url from S3
  getUrlFromS3(fileUrl, isViewFile) {
    console.log('dowload-file: ', fileUrl);

    // Setting up S3 download parameters
    const api_param = {
      key: fileUrl,
    };

    // Get file content type
    if (isViewFile) {
      const content_type = this.getContentType(fileUrl);
      if (content_type !== null) {
        api_param.content_type = content_type;
      }
    }

    return Methods.apiExecute('download-file', api_param)
      .then(response => {
        console.log('response = ', response);
        const s3FileInfo = response.data;
        return Promise.resolve(s3FileInfo);
      })
      .catch(error => {
        const ret = {
          status: 500,
          message: JSON.stringify(error),
        };
        return Promise.reject(ret);
      });
  },
  // Get view file url
  getFile(fileUrl) {
    console.log('dowload-file: ', fileUrl);
    const extentions = fileUrl.split('.').pop();
    if (extentions === 'txt') {
      return this.getDownloadUrl(fileUrl);
    }
    return this.getFileViewUrl(fileUrl);
  },
  // Get view file url
  getFileViewUrl(fileUrl) {
    console.log('dowload-file: ', fileUrl);
    return this.getUrlFromS3(fileUrl, true);
  },
  // Get download url
  getDownloadUrl(fileUrl) {
    console.log(`getDownloadUrl: ${JSON.stringify(fileUrl)}`);
    return this.getUrlFromS3(fileUrl, false);
  },
  readFileAllBrowsers(file) {
    return new Promise((resolve, reject) => {
      if (file) {
        const reader = new FileReader();
        reader.readAsArrayBuffer(file);
        reader.onload = evt => {
          resolve(evt.target.result);
        };
        reader.onerror = evt => {
          reject(evt);
        };
      } else {
        reject({});
      }
    });
  },
  string2ArrayBuffer(string, callback) {
    const bb = new BlobBuilder();
    bb.append(string);
    const f = new FileReader();
    f.onload = e => {
      callback(e.target.result);
    };
    f.readAsArrayBuffer(bb.getBlob());
  },
  arrayBuffer2String(buf, callback) {
    const bb = new BlobBuilder();
    bb.append(buf);
    const f = new FileReader();
    f.onload = e => {
      callback(e.target.result);
    };
    f.readAsText(bb.getBlob());
  },
  getContentType(filename) {
    const mimes = this.fileMimeTypeMapping();
    const extentions = filename.split('.').pop();
    let content_type = null;
    if (mimes.has(extentions)) {
      content_type = mimes.get(extentions);
    }
    console.log(`getContentType = ${content_type}`);
    return content_type;
  },
  // eslint-disable-next-line max-statements
  fileMimeTypeMapping() {
    const mime = new Map();
    mime.set('txt', 'text/plain');
    mime.set('3gp', 'video/3gpp');
    mime.set('7z', 'application/x-7z-compressed');
    mime.set('accdb', 'application/msaccess');
    mime.set('ai', 'application/illustrator');
    mime.set('apk', 'application/vnd.android.package-archive');
    mime.set('arw', 'image/x-dcraw');
    mime.set('avi', 'video/x-msvideo');
    mime.set('bash', 'text/x-shellscript');
    mime.set('bat', 'application/x-msdos-program');
    mime.set('blend', 'application/x-blender');
    mime.set('bin', 'application/x-bin');
    mime.set('bmp', 'image/bmp');
    mime.set('bpg', 'image/bpg');
    mime.set('bz2', 'application/x-bzip2');
    mime.set('cb7', 'application/x-cbr');
    mime.set('cba', 'application/x-cbr');
    mime.set('cbr', 'application/x-cbr');
    mime.set('cbt', 'application/x-cbr');
    mime.set('cbtc', 'application/x-cbr');
    mime.set('cbz', 'application/x-cbr');
    mime.set('cc', 'text/x-c');
    mime.set('cdr', 'application/coreldraw');
    mime.set('class', 'application/java');
    mime.set('cnf', 'text/plain');
    mime.set('conf', 'text/plain');
    mime.set('cpp', 'text/x-c++src');
    mime.set('cr2', 'image/x-dcraw');
    mime.set('css', 'text/css');
    mime.set('csv', 'text/csv');
    mime.set('cvbdl', 'application/x-cbr');
    mime.set('c', 'text/x-c');
    mime.set('c++', 'text/x-c++src');
    mime.set('dcr', 'image/x-dcraw');
    mime.set('deb', 'application/x-deb');
    mime.set('dng', 'image/x-dcraw');
    mime.set('doc', 'application/msword');
    mime.set('docm', 'application/vnd.ms-word.document.macroEnabled.12');
    mime.set(
      'docx',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
    mime.set('dot', 'application/msword');
    mime.set(
      'dotx',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.template'
    );
    mime.set('dv', 'video/dv');
    mime.set('eot', 'application/vnd.ms-fontobject');
    mime.set('epub', 'application/epub+zip');
    mime.set('eps', 'application/postscript');
    mime.set('erf', 'image/x-dcraw');
    mime.set('exe', 'application/x-ms-dos-executable');
    mime.set('flac', 'audio/flac');
    mime.set('flv', 'video/x-flv');
    mime.set('gif', 'image/gif');
    mime.set('gpx', 'application/gpx+xml');
    mime.set('gz', 'application/gzip');
    mime.set('gzip', 'application/gzip');
    mime.set('h', 'text/x-h');
    mime.set('heic', 'image/heic');
    mime.set('heif', 'image/heif');
    mime.set('hh', 'text/x-h');
    mime.set('hpp', 'text/x-h');
    mime.set('htaccess', 'text/plain');
    mime.set('ical', 'text/calendar');
    mime.set('ics', 'text/calendar');
    mime.set('iiq', 'image/x-dcraw');
    mime.set('impress', 'text/impress');
    mime.set('java', 'text/x-java-source');
    mime.set('jp2', 'image/jp2');
    mime.set('jpeg', 'image/jpeg');
    mime.set('jpg', 'image/jpeg');
    mime.set('jps', 'image/jpeg');
    mime.set('k25', 'image/x-dcraw');
    mime.set('kdc', 'image/x-dcraw');
    mime.set('key', 'application/x-iwork-keynote-sffkey');
    mime.set('keynote', 'application/x-iwork-keynote-sffkey');
    mime.set('kml', 'application/vnd.google-earth.kml+xml');
    mime.set('kmz', 'application/vnd.google-earth.kmz');
    mime.set('kra', 'application/x-krita');
    mime.set('ldif', 'text/x-ldif');
    mime.set('love', 'application/x-love-game');
    mime.set('lwp', 'application/vnd.lotus-wordpro');
    mime.set('m2t', 'video/mp2t');
    mime.set('m3u', 'audio/mpegurl');
    mime.set('m3u8', 'audio/mpegurl');
    mime.set('m4a', 'audio/mp4');
    mime.set('m4b', 'audio/m4b');
    mime.set('m4v', 'video/mp4');
    mime.set('markdown', 'text/markdown');
    mime.set('mdown', 'text/markdown');
    mime.set('md', 'text/markdown');
    mime.set('mdb', 'application/msaccess');
    mime.set('mdwn', 'text/markdown');
    mime.set('mkd', 'text/markdown');
    mime.set('mef', 'image/x-dcraw');
    mime.set('mkv', 'video/x-matroska');
    mime.set('mobi', 'application/x-mobipocket-ebook');
    mime.set('mov', 'video/quicktime');
    mime.set('mp3', 'audio/mpeg');
    mime.set('mp4', 'video/mp4');
    mime.set('mpeg', 'video/mpeg');
    mime.set('mpg', 'video/mpeg');
    mime.set('mpo', 'image/jpeg');
    mime.set('msi', 'application/x-msi');
    mime.set('mts', 'video/MP2T');
    mime.set('mt2s', 'video/MP2T');
    mime.set('nef', 'image/x-dcraw');
    mime.set('numbers', 'application/x-iwork-numbers-sffnumbers');
    mime.set('odf', 'application/vnd.oasis.opendocument.formula');
    mime.set('odg', 'application/vnd.oasis.opendocument.graphics');
    mime.set('odp', 'application/vnd.oasis.opendocument.presentation');
    mime.set('ods', 'application/vnd.oasis.opendocument.spreadsheet');
    mime.set('odt', 'application/vnd.oasis.opendocument.text');
    mime.set('oga', 'audio/ogg');
    mime.set('ogg', 'audio/ogg');
    mime.set('ogv', 'video/ogg');
    mime.set('one', 'application/msonenote');
    mime.set('opus', 'audio/ogg');
    mime.set('orf', 'image/x-dcraw');
    mime.set('otf', 'application/font-sfnt');
    mime.set('pages', 'application/x-iwork-pages-sffpages');
    mime.set('pdf', 'application/pdf');
    mime.set('pfb', 'application/x-font');
    mime.set('pef', 'image/x-dcraw');
    mime.set('php', 'application/x-php');
    mime.set('pl', 'application/x-perl');
    mime.set('pls', 'audio/x-scpls');
    mime.set('png', 'image/png');
    mime.set('pot', 'application/vnd.ms-powerpoint');
    mime.set('potm', 'application/vnd.ms-powerpoint.template.macroEnabled.12');
    mime.set(
      'potx',
      'application/vnd.openxmlformats-officedocument.presentationml.template'
    );
    mime.set('ppa', 'application/vnd.ms-powerpoint');
    mime.set('ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12');
    mime.set('pps', 'application/vnd.ms-powerpoint');
    mime.set('ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12');
    mime.set(
      'ppsx',
      'application/vnd.openxmlformats-officedocument.presentationml.slideshow'
    );
    mime.set('ppt', 'application/vnd.ms-powerpoint');
    mime.set(
      'pptm',
      'application/vnd.ms-powerpoint.presentation.macroEnabled.12'
    );
    mime.set(
      'pptx',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    );
    mime.set('ps', 'application/postscript');
    mime.set('psd', 'application/x-photoshop');
    mime.set('py', 'text/x-python');
    mime.set('raf', 'image/x-dcraw');
    mime.set('rar', 'application/x-rar-compressed');
    mime.set('reveal', 'text/reveal');
    mime.set('rss', 'application/rss+xml');
    mime.set('rtf', 'application/rtf');
    mime.set('rw2', 'image/x-dcraw');
    mime.set('schema', 'text/plain');
    mime.set('sgf', 'application/sgf');
    mime.set('sh-lib', 'text/x-shellscript');
    mime.set('sh', 'text/x-shellscript');
    mime.set('srf', 'image/x-dcraw');
    mime.set('sr2', 'image/x-dcraw');
    mime.set('tar', 'application/x-tar');
    mime.set('tar.bz2', 'application/x-bzip2');
    mime.set('tar.gz', 'application/x-compressed');
    mime.set('tbz2', 'application/x-bzip2');
    mime.set('tcx', 'application/vnd.garmin.tcx+xml');
    mime.set('tex', 'application/x-tex');
    mime.set('tgz', 'application/x-compressed');
    mime.set('tiff', 'image/tiff');
    mime.set('tif', 'image/tiff');
    mime.set('ttf', 'application/font-sfnt');
    mime.set('vcard', 'text/vcard');
    mime.set('vcf', 'text/vcard');
    mime.set('vob', 'video/dvd');
    mime.set('vsd', 'application/vnd.visio');
    mime.set('vsdm', 'application/vnd.ms-visio.drawing.macroEnabled.12');
    mime.set('vsdx', 'application/vnd.ms-visio.drawing');
    mime.set('vssm', 'application/vnd.ms-visio.stencil.macroEnabled.12');
    mime.set('vssx', 'application/vnd.ms-visio.stencil');
    mime.set('vstm', 'application/vnd.ms-visio.template.macroEnabled.12');
    mime.set('vstx', 'application/vnd.ms-visio.template');
    mime.set('wav', 'audio/wav');
    mime.set('webm', 'video/webm');
    mime.set('woff', 'application/font-woff');
    mime.set('wpd', 'application/vnd.wordperfect');
    mime.set('wmv', 'video/x-ms-wmv');
    mime.set('xcf', 'application/x-gimp');
    mime.set('xla', 'application/vnd.ms-excel');
    mime.set('xlam', 'application/vnd.ms-excel.addin.macroEnabled.12');
    mime.set('xls', 'application/vnd.ms-excel');
    mime.set('xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12');
    mime.set('xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12');
    mime.set(
      'xlsx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    mime.set('xlt', 'application/vnd.ms-excel');
    mime.set('xltm', 'application/vnd.ms-excel.template.macroEnabled.12');
    mime.set(
      'xltx',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.template'
    );
    mime.set('xrf', 'image/x-dcraw');
    mime.set('yaml', 'application/yaml');
    mime.set('yml', 'application/yaml');
    mime.set('zip', 'application/zip');
    mime.set('url', 'application/internet-shortcut');
    mime.set('webloc', 'application/internet-shortcut');
    mime.set('js', 'application/javascript');
    mime.set('json', 'application/json');
    mime.set('fb2', 'application/x-fictionbook+xml');
    mime.set('html', 'text/html');
    mime.set('htm', 'text/html');
    mime.set('m', 'text/x-matlab');
    mime.set('svg', 'image/svg+xml');
    mime.set('swf', 'application/octet-stream');
    mime.set('xml', 'application/xml');

    return mime;
  },
};
