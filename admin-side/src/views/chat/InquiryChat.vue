<template>
  <div v-bind:class="loading === true ? 'btn-disable' : null">
    <CCard>
      <CCardHeader>
        <strong>
          商品ID：<span class="pr-4">{{ manage_no }}</span>
        </strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <div style="margin: 0 30px">
            <div class="data-group" id="chat-area">
              <CRow v-for="(item, i) in chatData" :key="i">
                <template v-if="item.update_category_id === '2'">
                  <CCol sm="5">
                    <div class="chatText receiveChat">
                      <div class="mb-2">
                        {{ item.create_datetime }}　【質問者：{{
                          item.member_name
                        }}】
                      </div>
                      <div style="white-space: pre-line">
                        {{ item.message }}
                      </div>
                      <div class="form-inline">
                        <CButton
                          class="checkedBtn btn-status"
                          size="sm"
                          color="primary"
                          @click="answer(item.exhibition_message_no)"
                        >
                          回答する
                        </CButton>
                        <div
                          v-if="item.checked_admin_no !== null"
                          class="mt-4 ml-2"
                        >
                          回答済み
                        </div>
                      </div>
                    </div>
                  </CCol>
                </template>
                <template v-else>
                  <CCol sm="7"></CCol>
                  <CCol sm="5">
                    <div class="chatText sendChat">
                      <div class="mb-2">{{ item.create_datetime }}</div>
                      <div style="white-space: pre-line">
                        {{ item.message }}
                      </div>
                      <div class="mt-2">担当：{{ item.create_admin_name }}</div>
                    </div>
                  </CCol>
                </template>
              </CRow>
            </div>
            <div class="bottom-menu sticky">
              <CRow class="form-group">
                <CCol>
                  <div v-for="(text, i) in errorMsg" :key="i" class="text-red">
                    {{ text }}
                  </div>
                  <CFormTextarea
                    ref="chatInput"
                    id="chat-input"
                    class="form-group col-sm-12 mb-0 px-0-imp"
                    addInputClasses="fixed-size col-sm-12"
                    rows="6"
                    v-model="chatMessage"
                    @change="changeFlag = true"
                    :disabled="answerFlg"
                    placeholder="コメントを入力してください"
                  />
                </CCol>
              </CRow>
              <CRow style="margin-top: 15px;">
                <CCol sm="auto">
                  <CButton
                    class="mx-1 font-weight-bold"
                    color="light"
                    @click="goBack"
                  >
                    一覧に戻る
                  </CButton>
                </CCol>
                <CCol sm="7" class="d-flex align-items-center justify-content-end">
                  <span @click="getChatData">
                    <CIcon :height="30" name="cil-reload" class="reload-icon" />
                  </span>
                </CCol>
                <CCol sm="auto">
                  <CButton
                    size="lg"
                    color="dark"
                    class="btn-status"
                    @click="reset"
                    :disabled="answerFlg"
                  >
                    クリア
                  </CButton>
                </CCol>
                <CCol sm="auto">
                  <CButton
                    size="lg"
                    color="primary"
                    class="btn-status"
                    @click="regist"
                    :disabled="answerFlg"
                  >
                    投稿する
                  </CButton>
                </CCol>
              </CRow>
            </div>
          </div>
        </CForm>
      </CCardBody>
    </CCard>
  </div>
</template>

<script>
  import Methods from '@/api/methods';

  export default {
    name: 'chat',
    data() {
      return {
        exhibition_item_no: null,
        manage_no: null,
        itemData: {},
        chatData: {},
        chatMessage: null,
        answerFlg: true,
        selectExhibitionMessageNo: null,
        loading: false,
        errorMsg: [],
      };
    },
    mounted() {
      if (this.$route.params.id) {
        console.log(this.$route.params.id);
        this.getChatData()
          .then(() => {
            this.$nextTick(() => {
              const element = document.getElementById('chat-input');
              if (element) {
                element.focus();
              }
            });
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          });
      } else {
        this.goBack();
      }
    },
    methods: {
      getChatData() {
        this.loading = true;
        this.exhibition_item_no = this.$route.params.id;
        this.manage_no = this.$route.params.manageNo;
        const search_condition = {
          exhibition_item_no: this.exhibition_item_no,
        };
        return Methods.apiExecute('get-inquiry-chat', search_condition).then(
          response => {
            console.log(response)
            this.loading = false;
            this.chatData =
              response.data && response.data.length > 0 ? response.data : {};

            this.$nextTick(() => {
              const chatArea = document.getElementById('chat-area');
              if (chatArea) {
                const elements = chatArea.querySelectorAll('div.chatText');
                if (elements.length > 0) {
                  const lastElement = elements[elements.length - 1];
                  // 最後の要素を中央に表示
                  lastElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                  });
                }
              }
            });
            return Promise.resolve();
          }
        );
      },
      answer(exhibition_message_no) {
        this.answerFlg = false;
        this.selectExhibitionMessageNo = exhibition_message_no;
        this.$nextTick(() => {
          const element = document.getElementById('chat-input');
          if (element) {
            element.focus();
          }
        });
      },
      regist() {
        if (this.chatMessage) {
          this.loading = true;
          this.errorMsg = [];
          const params = {
            exhibition_item_no: this.exhibition_item_no,
            exhibition_message_no: this.selectExhibitionMessageNo,
            chat_message: this.chatMessage,
          };
          Methods.apiExecute('regist-inquiry-chat', params)
            .then(response => {
              if (response.status === 200) {
                this.chatMessage = null;
                this.loading = false;
                this.answerFlg = true;
                this.getChatData();
              }
            })
            .catch(error => {
              this.loading = false;
              this.errorMsg = Methods.parseHtmlResponseError(
                this.$router,
                error
              );
            });
        }
      },
      reset() {
        this.chatMessage = null;
        this.errorMsg = [];
      },
      goBack() {
        this.$router.go(-1);
      },
    },
  };
</script>

<style type="text/css">
  .chatText {
    border: 1px solid;
    padding: 10px;
    margin: 5px 0;
  }

  .receiveChat {
    background: #e2f0d9;
  }

  .sendChat {
    background: #deebf7;
  }

  .btn-confirm {
    margin: 5px 10px;
  }

  .bottom-menu {
    background: #fff;
    padding-top: 5px;
    padding-bottom: 5px;
    margin-right: 0;
    margin-left: 0;
  }

  .sticky {
    position: sticky;
    bottom: 0;
  }

  .fixed-size {
    resize: none;
  }

  .checkedBtn {
    margin-top: 20px;
    width: auto;
    min-width: 100px;
    white-space: nowrap;
  }

  .data-group {
    border: solid 1px black;
    padding: 5px 20px;
    margin-bottom: 10px;
    overflow-y: auto;
    /* overflow-x: hidden; */
    height: 500px;
  }

  .text-red {
    color: red;
  }

  .reload-icon {
    color: #ffff;
    background-color: #02b092d4;
    border-radius: 50%;
    padding: 5px;
    height: 100%;
    cursor: pointer;
  }

  .btn-disable {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.5;
  }
</style>
