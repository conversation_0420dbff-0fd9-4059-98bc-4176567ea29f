<template>
  <div class="mb-3">
    <CCard class="mb-3">
      <CCardHeader>
        <strong>項目設定管理</strong>
      </CCardHeader>
      <CCardBody>
        <CForm>
          <CRow class="mb-3">
            <CCol sm="2"> リソース選択 </CCol>
            <CCol sm="3">
              <CFormSelect
                name="resource_type"
                :options="resourceOptions"
                v-model="search_condition.resource_type"
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol sm="2"> 言語 </CCol>
            <CCol sm="3" class="form-inline">
              <CFormSelect
                name="language_code"
                :options="languageOptions"
                v-model="search_condition.language_code"
                class="form-group col-sm-3 pl-0"
                addInputClasses="w-100"
              />
            </CCol>
          </CRow>
        </CForm>

        <CRow class="align-items-center mt-4">
          <CCol sm="5"></CCol>
          <CCol sm="2" class="mb-xl-0 text-right d-grid">
            <CButton size="sm" color="info" @click="search" block>検索</CButton>
          </CCol>
          <CCol sm="1" />
          <CCol sm="2"></CCol>
          <CCol sm="2"></CCol>
        </CRow>
      </CCardBody>
    </CCard>
    <CRow>
      <CCol sm="12">
        <ProductInfoTable
          ref="productInfoTableRef"
          name="resourceList"
          :items="resourceList"
          :total_count="totalCount"
          :current_count="currentCount"
          hover
          striped
          border
          small
          fixed
          :loading="loading"
          :parentConstantKey="ConstantKeyOptions"
          caption="項目一覧"
          @add-new-item="addNewItem"
          @delete-item="deleteConfirm"
          @edit-item="editItem"
        />
      </CCol>
    </CRow>

    <CModal
      color="primary"
      :visible="isErrorDialog"
      :closeOnBackdrop="false"
      @close="
        () => {
          isErrorDialog = false;
          errorMsg = [];
        }
      "
    >
      <CModalHeader>
        <CModalTitle>確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div v-for="(val, i) in errorMsg" :key="i">{{ val }}</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          color="dark"
          @click="
            () => {
              isErrorDialog = false;
              errorMsg = [];
            }
          "
        >
          閉じる
        </CButton>
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="compModal"
      @close="
        () => {
          compModal = false;
        }
      "
    >
      <CModalHeader :closeButton="false">
        <CModalTitle>完了</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>処理が完了しました。</div>
      </CModalBody>
      <CModalFooter>
        <CButton
          @click="
            () => {
              compModal = false;
            }
          "
          color="dark"
          >閉じる</CButton
        >
      </CModalFooter>
    </CModal>

    <CModal
      backdrop="static"
      :keyboard="false"
      :visible="deleteModal"
      @close="
        () => {
          deleteModal = false;
        }
      "
    >
      <CModalHeader>
        <CModalTitle>削除確認</CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div>この項目を削除してもよろしいですか？</div>
      </CModalBody>
      <CModalFooter>
        <CButton @click="deleteModal = false" color="dark">キャンセル</CButton>
        <CButton @click="deleteField" color="primary">OK</CButton>
      </CModalFooter>
    </CModal>
  </div>
</template>

<script>
  import Methods from '@/api/methods';
import Base from '@/common/base';
import {ScaleLoader} from '@/components/Table';
import {useCommonStore} from '@/store/common';
import ProductInfoTable from '../../components/tenant/product/ProductInfoTable.vue';

  export default {
    name: 'ProductInfo',
    components: {
      ProductInfoTable,
      ScaleLoader,
    },
    setup() {
      const store = useCommonStore();
      return {store};
    },
    data() {
      return {
        loading: true,
        // 検索条件
        resourceOptions: [],
        languageOptions: [],

        // 定数キー
        ConstantKeyOptions: [],

        // Screen params
        resourceList: [],
        search_condition: {
          resource_type: 'item',
          language_code: '',
        },

        // request params
        requestData: {},

        // delete item
        deleteItem: {},

        // Counting
        current_count: 0,
        total_count: 0,

        // Error dialog
        isErrorDialog: false,
        errorMsg: [],

        // Complete dialog
        compModal: false,

        // Delete dialog
        deleteModal: false,

        // 子コンポーネントの参照
        productInfoTableRef: null,

        // 定数
        constResourceList: [
          {value: 'item', label: '商品'},
          {value: 'member', label: '会員'},
        ],

        // 言語リスト
        constLanguageOptions: [],
      };
    },
    beforeRouteEnter(to, from, next) {
      next(vm => {
        vm.prevRoute = from;

        // 初期化
        vm.store.set([
          'resourceSearchCondition',
          {resource_type: 'item', language_code: ''},
        ]);
      });
    },
    mounted() {
      this.loading = true;
      this.getConstants()
        .then(() => {
          this.getConstantKey()
            .then(() => {
              this.getResourceList()
                .then(postage => {
                  this.resourceList = postage;
                  this.loading = false;
                })
                .catch(error => {
                  console.log(error);
                  this.loading = false;
                  this.errorMsg = Methods.parseHtmlResponseError(
                    this.$router,
                    error
                  );
                  this.isErrorDialog = true;
                });
              const self = this;
              setTimeout(() => {
                if (
                  self.position &&
                  document.getElementById(`item_${self.position}`)
                ) {
                  document
                    .getElementById(`item_${self.position}`)
                    .scrollIntoView({
                      behavior: 'smooth',
                      block: 'center',
                      inline: 'center',
                    });
                }
              }, 1000);
            })
            .catch(error => {
              console.log(error);
              this.loading = false;
              this.errorMsg = Methods.parseHtmlResponseError(
                this.$router,
                error
              );
              this.isErrorDialog = true;
            });
        })
        .catch(error => {
          console.log(error);
          this.loading = false;
          this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
          this.isErrorDialog = true;
        });
    },
    watch: {
      search_condition: {
        handler(newVal) {
          this.store.set(['resourceSearchCondition', newVal]);
        },
        deep: true,
        immediate: false,
      },
    },
    computed: {
      totalCount() {
        return Base.number2string(this.total_count);
      },
      currentCount() {
        return Base.number2string(this.current_count);
      },
    },
    methods: {
      getConstants() {
        const request = {
          key_strings: ['LANGUAGE_CODE'],
        };
        return Methods.apiExecute('get-constants-by-keys', request)
          .then(response => {
            if (response.status === 200) {
              // 言語
              this.constLanguageOptions = [];
              for (const constant of response.data) {
                switch (constant.key_string) {
                  case 'LANGUAGE_CODE':
                    this.constLanguageOptions.push({
                      value: constant.value1,
                      label: constant.value2,
                    });
                    break;
                  default:
                    break;
                }
              }

              return Methods.apiExecute('get-tenant-language-list', {}).then(
                response => {
                  if (response.status === 200) {
                    // リソース選択
                    this.constResourceList.forEach(item => {
                      this.resourceOptions.push(item);
                    });

                    // 言語
                    this.languageOptions = response.data.language_code_list.map(
                      cd => {
                        const lang_list = this.constLanguageOptions.find(
                          item => item.value === cd
                        );
                        return {
                          value: cd,
                          label: lang_list ? lang_list.label : '',
                        };
                      }
                    );

                    // 言語コードの初期値を設定（最初の言語を使用）
                    if (this.languageOptions.length > 0) {
                      this.search_condition.language_code = this.languageOptions[0].value;
                      // storeも更新
                      this.store.set([
                        'resourceSearchCondition',
                        {resource_type: 'item', language_code: this.languageOptions[0].value},
                      ]);
                    }
                  }
                  return Promise.resolve();
                }
              );
            }
            return Promise.resolve();
          });
      },
      getConstantKey() {
        const request = {};
        return Methods.apiExecute('get-constant-key', request).then(
          response => {
            if (response.status === 200) {
              this.ConstantKeyOptions = [];
              this.ConstantKeyOptions.push({label: '', value: ''});
              response.data.map(constant => {
                this.ConstantKeyOptions.push({
                  label: constant.key_string,
                  value: constant.key_string,
                });
              });
            }
            return Promise.resolve();
          }
        );
      },
      search() {
        this.isErrorDialog = false;
        this.loading = true;
        if (
          this.search_condition.resource_type === '' &&
          this.search_condition.language_code === ''
        ) {
          this.errorMsg = ['1つ以上の条件を選択してください。'];
          this.isErrorDialog = true;
          return;
        }
        this.getResourceList()
          .then(postage => {
            this.resourceList = postage;
            this.loading = false;
          })
          .catch(error => {
            this.loading = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
            this.isErrorDialog = true;
          });
      },
      getResourceList() {
        // Request to server
        return Methods.apiExecute('get-field-list', this.search_condition).then(
          response => {
            if (response.status === 200) {
              const resourceList = response.data.data;
              this.total_count = response.data
                ? response.data.total_count || 0
                : 0;
              this.current_count = response.data
                ? response.data.current_count || 0
                : 0;
              return Promise.resolve(resourceList);
            }
            return Promise.resolve(null);
          }
        );
      },
      // 新規追加処理
      async addNewItem(item) {
        this.loading = true;
        this.requestData = {
          field_division: this.search_condition.resource_type,
          language_code: this.search_condition.language_code,
          ...item,
          order_no: this.resourceList.length + 1,
        };
        // Booleanを0と1に変換
        this.requestData.required_flag = this.requestData.required_flag ? 1 : 0;
        // エラーチェック、不要なパラメータ削除
        this.paramCheck();

        if (this.errorMsg.length > 0) {
          this.loading = false;
          this.isErrorDialog = true;
        } else {
          await Methods.apiExecute('regist-field-item', this.requestData)
            .then(response => {
              if (response.status === 200) {
                // 一覧の再取得
                this.getResourceList()
                  .then(postage => {
                    this.resourceList = postage;
                    this.compModal = true;
                    this.loading = false;
                  })
                  .catch(error => {
                    this.loading = false;
                    this.errorMsg = Methods.parseHtmlResponseError(
                      this.$router,
                      error
                    );
                  });
              }
              // 新規項目追加の入力欄の中身をリセット
              if (this.$refs.productInfoTableRef) {
                this.$refs.productInfoTableRef.resetItem();
              }
              return Promise.resolve();
            })
            .catch(error => {
              this.loading = false;
              this.compModal = false;
              this.isErrorDialog = true;
              this.errorMsg = Methods.parseHtmlResponseError(
                this.$router,
                error
              );
              return Promise.resolve();
            });
        }
      },
      deleteConfirm(item) {
        this.deleteModal = true;
        this.deleteItem = item;
      },
      async deleteField() {
        this.loading = true;
        const params = {
          field_no: this.deleteItem.field_no,
          field_localized_no: this.deleteItem.field_localized_no,
          language_code: this.search_condition.language_code,
        };
        await Methods.apiExecute('delete-field-item', params)
          .then(response => {
            if (response.status === 200) {
              // 一覧の再取得
              this.getResourceList()
                .then(postage => {
                  this.resourceList = postage;
                  this.loading = false;
                })
                .catch(error => {
                  this.loading = false;
                  this.errorMsg = Methods.parseHtmlResponseError(
                    this.$router,
                    error
                  );
                });
              this.deleteModal = false;
              this.compModal = true;
            }
            return Promise.resolve();
          })
          .catch(error => {
            this.loading = false;
            this.deleteModal = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
            this.isErrorDialog = true;
            return Promise.resolve();
          });
      },
      paramCheck() {
        this.errorMsg = [];
        if (
          !this.requestData.logical_name ||
          this.requestData.logical_name.length === 0
        ) {
          this.errorMsg.push('項目名は必須です。');
        }
        if (
          !this.requestData.physical_name ||
          this.requestData.physical_name.length === 0
        ) {
          this.errorMsg.push('物理名は必須です。');
        }
        if (
          !this.requestData.input_type ||
          this.requestData.input_type.length === 0
        ) {
          this.errorMsg.push('入力方法は必須です。');
        }
        if (
          !this.requestData.data_type ||
          this.requestData.data_type.length === 0
        ) {
          this.errorMsg.push('入力制限は必須です。');
        } else {
          if (
            this.requestData.data_type === 'constant' &&
            (!this.requestData.input_data_list ||
              this.requestData.input_data_list.length === 0)
          ) {
            this.errorMsg.push('定数キーは必須です。');
          }
        }
        this.resourceList.forEach(item => {
          if (item.physical_name === this.requestData.physical_name) {
            this.errorMsg.push('同一の物理名は登録できません。');
          }
        });

        if (this.errorMsg.length === 0) {
          if (this.requestData.data_type === 'constant') {
            this.requestData.max_length = null;
            this.requestData.max_value = null;
            this.requestData.regular_expressions = null;
            // オブジェクト型に変換
            this.requestData.input_data_list = {
              key_string: this.requestData.input_data_list,
            };
          } else if (this.requestData.data_type === 'character') {
            this.requestData.input_data_list = null;
            this.requestData.max_value = null;
            this.requestData.regular_expressions = null;
          } else if (
            this.requestData.data_type === 'integer' ||
            this.requestData.data_type === 'float'
          ) {
            this.requestData.input_data_list = null;
            this.requestData.max_length = null;
            this.requestData.regular_expressions = null;
          } else if (this.requestData.data_type === 'reg') {
            this.requestData.input_data_list = null;
            this.requestData.max_length = null;
            this.requestData.max_value = null;
          }
        }
      },
      async editItem(item) {
        this.loading = true;
        // 編集の場合、言語コードは考慮せずにチェック
        const params = {
          field_no: item.field_no,
          language_code: null,
        };
        await Methods.apiExecute('check-field-item-used', params)
          .then(response => {
            this.loading = false;
            if (response.status === 200) {
              if (!response.data.is_used) {
                this.$router.push(
                  `/tenant/product-info/${item.field_localized_no}/edit`
                );
              } else {
                this.errorMsg.push(
                  '指定された項目は、項目表示設定管理またはCSV項目設定管理で使用しているため変更できません。'
                );
                this.isErrorDialog = true;
              }
            }
            return Promise.resolve();
          })
          .catch(error => {
            this.loading = false;
            this.deleteModal = false;
            this.errorMsg = Methods.parseHtmlResponseError(this.$router, error);
            this.isErrorDialog = true;
            return Promise.resolve();
          });
      },
    },
  };
</script>
