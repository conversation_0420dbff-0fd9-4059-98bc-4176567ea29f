CREATE TABLE t_item (
    item_no bigint,
    tenant_no bigint NOT NULL,
    manage_no character varying NOT NULL,
    area_id character varying(2),
    status integer NOT NULL, -- 0:在庫表示 1:在庫非表示 2:新着 3:成約(SOLDOUT) 4:成約(非表示)
    new_start_datetime timestamp with time zone,
    new_end_datetime timestamp with time zone,
    recommend_start_datetime timestamp with time zone,
    recommend_end_datetime timestamp with time zone,
    price_display_flag integer NOT NULL,
    linked_flag integer NOT NULL DEFAULT 0,
    linked_datetime timestamp with time zone,
    favorite_count integer DEFAULT 0,
    view_count integer DEFAULT 0,
    image text[],
    create_admin_no bigint,
    create_user_no bigint,
    create_datetime timestamp with time zone DEFAULT now(),
    update_admin_no bigint,
    update_user_no bigint,
    update_datetime timestamp with time zone DEFAULT now(),
    delete_flag integer DEFAULT 0 -- 0:有効 1:削除済み
);

COMMENT ON TABLE t_item IS '商品';
COMMENT ON COLUMN t_item.item_no IS '商品番号';
COMMENT ON COLUMN t_item.tenant_no IS 'テナント番号';
COMMENT ON COLUMN t_item.manage_no IS '管理番号';
COMMENT ON COLUMN t_item.area_id IS '資材置き場';
COMMENT ON COLUMN t_item.status IS '商品ステータス';
COMMENT ON COLUMN t_item.new_start_datetime IS '新着表示開始日時';
COMMENT ON COLUMN t_item.new_end_datetime IS '新着表示終了日時';
COMMENT ON COLUMN t_item.recommend_start_datetime IS 'おすすめ開始日時';
COMMENT ON COLUMN t_item.recommend_end_datetime IS 'おすすめ終了日時';
COMMENT ON COLUMN t_item.price_display_flag IS '価格表示フラグ';
COMMENT ON COLUMN t_item.linked_flag IS '連携済みフラグ';
COMMENT ON COLUMN t_item.linked_datetime IS '連携日時';
COMMENT ON COLUMN t_item.image IS 'イメージ';
COMMENT ON COLUMN t_item.create_admin_no IS '作成管理者番号';
COMMENT ON COLUMN t_item.create_user_no IS '作成利用者番号';
COMMENT ON COLUMN t_item.create_datetime IS '作成日時';
COMMENT ON COLUMN t_item.update_admin_no IS '更新管理者番号';
COMMENT ON COLUMN t_item.update_user_no IS '更新利用者番号';
COMMENT ON COLUMN t_item.update_datetime IS '更新日時';
COMMENT ON COLUMN t_item.delete_flag IS '削除フラグ';

CREATE SEQUENCE public.t_item_item_no_seq
INCREMENT 1
START 1
MINVALUE 1
MAXVALUE 9223372036854775807
CACHE 1;

ALTER TABLE ONLY t_item
ALTER COLUMN item_no SET DEFAULT nextval('t_item_item_no_seq'::regclass);

ALTER TABLE ONLY t_item
ADD CONSTRAINT t_item_no_pkey PRIMARY KEY (item_no);

ALTER TABLE ONLY t_item
ADD CONSTRAINT t_item_tenant_no_fkey FOREIGN KEY (
    tenant_no
) REFERENCES m_tenant(tenant_no) ON UPDATE RESTRICT ON DELETE RESTRICT;
