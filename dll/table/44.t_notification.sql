CREATE TABLE t_notification (
 notification_no bigint , --通知番号
 tenant_no bigint  NOT NULL, --テナント番号
 member_no bigint NOT NULL, --会員番号
 notification_category_id character varying, -- 通知の種類
 message character varying,
 is_read boolean DEFAULT FALSE NOT NULL,
 link_url character varying,
 create_user_no bigint, --作成者
 create_admin_no bigint, --作成管理者
 create_datetime timestamp with time zone NOT NULL default now(), --作成日時
 delete_flag integer DEFAULT 0 NOT NULL,
PRIMARY KEY (notification_no));

COMMENT ON TABLE t_notification IS '通知';
COMMENT ON COLUMN t_notification.notification_no IS '通知番号';
COMMENT ON COLUMN t_notification.tenant_no IS 'テナント番号';
COMMENT ON COLUMN t_notification.member_no IS '会員番号';
COMMENT ON COLUMN t_notification.notification_category_id IS '通知の種類';
COMMENT ON COLUMN t_notification.message IS '通知メッセージ';
COMMENT ON COLUMN t_notification.is_read IS '既読かどうか';
COMMENT ON COLUMN t_notification.link_url IS '通知のリンク先URL';
COMMENT ON COLUMN t_notification.create_user_no IS '作成利用者番号';
COMMENT ON COLUMN t_notification.create_admin_no IS '作成管理者番号';
COMMENT ON COLUMN t_notification.create_datetime IS '作成日時';
COMMENT ON COLUMN t_notification.delete_flag IS '削除フラグ';

CREATE SEQUENCE t_notification_notification_no_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE ONLY t_notification ALTER COLUMN notification_no SET DEFAULT nextval('t_notification_notification_no_seq'::regclass);
