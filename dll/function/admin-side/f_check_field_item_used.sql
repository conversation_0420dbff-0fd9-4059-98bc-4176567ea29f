CREATE OR REPLACE FUNCTION public.f_check_field_item_used(
    in_field_no bigint,
    in_tenant_no bigint,
    in_language_code character varying
)
RETURNS TABLE(
    is_exists boolean
)
LANGUAGE plpgsql
AS $BODY$
----------------------------------------------------------------------------------------------------
-- 項目表示設定、CSV項目設定で更新対象のfield_noを使用状況確認
-- Parameters
-- @param in_field_no bigint - 項目番号
-- @param in_tenant_no bigint - テナント番号
-- @param in_language_code character varying - 言語コード
----------------------------------------------------------------------------------------------------

BEGIN

  -- NULLチェックを追加
  IF in_field_no IS NULL THEN
    RETURN QUERY SELECT false AS is_exists;
    RETURN;
  END IF;

  RETURN QUERY
  WITH
  -- チェック対象の項目が存在するかを確認
  exist_field_mapping AS (
    SELECT EXISTS (
      SELECT 1
      FROM m_field_mapping fm
      WHERE fm.tenant_no = in_tenant_no
      AND (in_language_code IS NULL OR fm.language_code = in_language_code)
      AND in_field_no = ANY(fm.field_no)
      AND fm.delete_flag = 0
    ) AS is_field_mapping
  ),
  exist_field_csv AS (
    SELECT EXISTS (
      SELECT 1
      FROM m_field_csv fc
      WHERE fc.tenant_no = in_tenant_no
      AND (in_language_code IS NULL OR fc.language_code = in_language_code)
      AND in_field_no = ANY(fc.input_data_list)
      AND fc.delete_flag = 0
    ) AS is_field_csv
  )
  SELECT
    COALESCE((SELECT is_field_mapping FROM exist_field_mapping), false) OR
    COALESCE((SELECT is_field_csv FROM exist_field_csv), false) AS is_exists;

END;
$BODY$;
