CREATE OR REPLACE FUNCTION public.f_get_exhibition_email_language_list(
    in_tenant_no bigint,
    in_exhibition_name character varying
)
RETURNS TABLE(
    exhibition_email_no bigint,
    exhibition_name character varying,
    classification bigint,
    send_datetime character varying,
    send_flag bigint,
    sent_flag integer,
    email_language_json json[]
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE


----------------------------------------------------------------------------------------------------
-- 入札会のメール情報を取得する
-- Parameters
-- @param in_tenant_no bigint
-- @param in_exhibition_name character varying
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  SELECT
      ee.exhibition_email_no AS exhibition_email_no,
      ee.exhibition_name AS exhibition_name,
      ee.classification AS classification,
      TO_CHAR(ee.send_datetime, 'YYYY-MM-DD HH24:MI:SS')::character varying AS send_datetime,
      ee.send_flag AS  send_flag,
      ee.sent_flag AS  sent_flag,
      array_agg(json_build_object(
        'language_code'         , eel.language_code ,
        'exhibition_email_localized_no' , eel.exhibition_email_localized_no ,
        'title' , eel.title ,
        'body' , eel.body ,
        'footer' , eel.footer ,
        'file' , eel.file
        ) ORDER BY eel.exhibition_email_localized_no)
	FROM t_exhibition_email ee
    LEFT JOIN t_exhibition_email_localized eel
      ON eel.tenant_no = in_tenant_no
      AND eel.exhibition_email_no = ee.exhibition_email_no
      AND eel.delete_flag = 0
    WHERE ee.tenant_no = in_tenant_no
    AND ee.exhibition_name = in_exhibition_name
    GROUP BY ee.exhibition_email_no, ee.exhibition_name, ee.classification, ee.send_datetime, ee.send_flag, ee.sent_flag ;

END;
$BODY$;
