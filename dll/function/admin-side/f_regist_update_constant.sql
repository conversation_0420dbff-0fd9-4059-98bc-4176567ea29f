CREATE OR REPLACE FUNCTION public.f_regist_update_constant(
    in_constant_no bigint,
    in_tenant_no bigint,
    in_key_string character varying,
    in_value_name character varying,
    in_sort_order integer,
    in_description character varying,
    in_start_datetime timestamp with time zone,
    in_end_datetime timestamp with time zone,
    in_admin_no bigint,
    in_chilConstants json[],
    OUT result boolean,
    OUT status integer,
    OUT message character varying)
RETURNS SETOF record
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$
DECLARE

-- 定数を更新する
-- Parameters
-- @param in_constant_id key bigint
-- @param in_tenant_no key bigint
-- @param in_key_string key character varying
-- @param in_value_name key character varying
-- @param in_description character varying
-- @param in_sorting integer
-- @param in_start_datetime  timestamp with time zone
-- @param in_end_datetime  timestamp with time zone
-- @param in_admin_id character varying
----------------------------------------------------------------------------------------------------

BEGIN

    WITH
    exist_constant_data AS (
        UPDATE m_constant CST
        SET key_string = in_key_string,
        value_name = in_value_name,
        sort_order = in_sort_order,
        description = in_description,
        start_datetime = in_start_datetime,
        end_datetime = in_end_datetime,
        update_admin_no = in_admin_no,
        update_datetime = now()
        WHERE CST.constant_no = in_constant_no
    RETURNING
        CST.constant_no
    ),

    new_constant_data AS (
        INSERT INTO m_constant (
        tenant_no,
        key_string,
        value_name,
        mainte_impossible_flag,
        sort_order,
        description,
        start_datetime,
        end_datetime,
        create_admin_no,
        create_datetime,
        update_admin_no,
        update_datetime
    )
    (
        SELECT
            in_tenant_no,
            in_key_string,
            in_value_name,
            1,
            in_sort_order,
            in_description,
            in_start_datetime,
            in_end_datetime,
            in_admin_no,
            now(),
            in_admin_no,
            now()
        WHERE
            in_constant_no IS NULL
    )
	RETURNING
        constant_no
    ),

    -- 既存レコードが存在するかチェック
    existing_constant_check AS (
        SELECT 1
        FROM m_constant_localized
        WHERE constant_localized_no = in_constant_no
    ),

    new_constant_localized_data AS (
    INSERT INTO m_constant_localized (
        tenant_no,
        constant_no,
        language_code,
        value1,
        value2,
        value3,
        value4,
        value5,
        file_url,
        create_admin_no,
        create_datetime,
        update_admin_no,
        update_datetime
    )
    (
    SELECT
        in_tenant_no,
        COALESCE((SELECT new_constant_data.constant_no FROM new_constant_data), in_constant_no),
        (data->>'languageCode')::character varying,
        (data->>'value1')::character varying,
        (data->>'value2')::character varying,
        (data->>'value3')::character varying,
        (data->>'value4')::character varying,
        (data->>'value5')::character varying,
        (data->>'fileUrl')::character varying,
        (data->>'adminNo')::bigint,
        now(),
        (data->>'adminNo')::bigint,
        now()
        FROM unnest(in_chilConstants::jsonb[]) AS data
        WHERE
            (data->>'constantLocalizedNo') IS NULL
    )
    RETURNING
        m_constant_localized.constant_localized_no
    ),

    exit_constant_localized_data AS (
    UPDATE m_constant_localized
    SET value1 = (data->>'value1')::character varying,
        value2 = (data->>'value2')::character varying,
        value3 = (data->>'value3')::character varying,
        value4 = (data->>'value4')::character varying,
        value5 = (data->>'value5')::character varying,
        file_url = (data->>'fileUrl')::character varying,
        update_admin_no = (data->>'adminNo')::bigint,
        update_datetime = now()
        FROM unnest(in_chilConstants::jsonb[]) AS data
    WHERE m_constant_localized.constant_localized_no = (data->>'constantLocalizedNo')::bigint
        AND (data->>'constantLocalizedNo') IS NOT NULL
    RETURNING
        m_constant_localized.constant_localized_no
    )

    SELECT 200 INTO status FROM exist_constant_data, new_constant_data, new_constant_localized_data, exit_constant_localized_data;
    result := true;
    status := 200;
    message := '';

RETURN NEXT;

EXCEPTION

    --その他エラー
    WHEN OTHERS THEN
    result := false;
    status := 500;
    message := SQLERRM;
    RETURN NEXT;

END;

$BODY$;
