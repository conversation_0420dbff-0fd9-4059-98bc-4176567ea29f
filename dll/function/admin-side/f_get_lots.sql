CREATE OR REPLACE FUNCTION public.f_get_lots (
  in_tenant_no bigint,
  in_exhibition_no bigint,
  in_manage_no character varying,
  in_productname character varying,
  in_category character varying,
  in_start_datetime_from character varying,
  in_start_datetime_to character varying,
  in_end_datetime_from character varying,
  in_end_datetime_to character varying,
  in_language_code character varying
)
RETURNS TABLE(
    row_number bigint,
    item_no bigint,
    manage_no character varying,
    -- category_id integer,
    tenant_no bigint,
    recommend_flag boolean,
    lot_no bigint,
    lot_id character varying,
    -- localized_json_array json[],
    localized_json_array text,
    lowest_bid_price numeric,
    lowest_bid_quantity numeric,
    lowest_bid_accept_price numeric,
    lowest_bid_accept_quantity numeric,
    quantity numeric,
    start_datetime text,
    end_datetime text,
    cancel_flag integer,
    bid_count integer,
    order_no integer,
    default_end_datetime text,
    top_price numeric,
    current_price numeric,
    hummer_flag integer,
    no_read_inquiry boolean
)
LANGUAGE plpgsql

COST 100
VOLATILE
ROWS 1000
AS $BODY$

DECLARE

----------------------------------------------------------------------------------------------------
-- 出展商品情報を取得する
----------------------------------------------------------------------------------------------------

BEGIN

  RETURN QUERY
  WITH
  all_category_constants AS (
    SELECT mcl.value1, mcl.value2, unnest(regexp_split_to_array(mcl.value3,',')) as value3
    FROM m_constant mc
      INNER JOIN m_constant_localized mcl
          ON mcl.constant_no = mc.constant_no
    WHERE mc.tenant_no = 1
      AND mcl.language_code = 'ja'
      AND mc.key_string = 'PRODUCT_CATEGORY'
      AND (now() BETWEEN COALESCE(mc.start_datetime, to_date('1900/01/01', 'YYYY/MM/DD'))
      AND COALESCE(mc.end_datetime, to_date('9999/12/31', 'YYYY/MM/DD')))
      AND mcl.value1 <> 'ct11'
  ),
  combined_categories AS (
    SELECT category_codes.item_no,
          array_agg(category_codes.category_code) as category_code,
          array_agg(ACC.value1) as category_id,
          array_agg(ACC.value2) as category_label
      FROM (
        SELECT t_item_localized.item_no, jsonb_array_elements(free_field->'categories')->>'category_code' AS category_code
      FROM t_item_localized
      UNION
      SELECT  t_item_localized.item_no,free_field->'basic_category'->>'category_code' AS category_code
        FROM t_item_localized
      ) AS category_codes
    LEFT JOIN all_category_constants ACC
        ON ACC.value3 = category_codes.category_code
    GROUP BY category_codes.item_no
  ),
  localized AS (
    SELECT TI.item_no
        , TI.manage_no
        , TI.recommend_start_datetime
        , MAX(TEI.quantity)::numeric as quantity --数量
        , MAX(TEI.lowest_bid_quantity)::numeric as lowest_bid_quantity --最低入札数量
        , MAX(TEI.lowest_bid_accept_quantity)::numeric as lowest_bid_accept_quantity --最低落札数量
        , array_agg(TIL.free_field ->>'product_name'::text) as productName
        , array_agg(array_to_string(CC.category_id, '')) as category_id
        , array_agg(array_to_string(CC.category_label, '')) as category_label
        , array_agg(TIL.free_field ->>'category'::text) as category
        , json_agg(
          json_build_object(
            'f1', TIL.language_code,
            'f2', json_build_object(
              'productName', TIL.free_field ->> 'product_name',
              'maker', TIL.free_field ->> 'maker',
              'rank', TIL.free_field ->> 'rank',
              'category', array_to_string(CC.category_label, ''),
              'image_url', TIL.free_field->>'image_url'
            )
          )
        ) AS localized_json_array
      FROM t_item TI
    INNER JOIN t_item_localized TIL
            ON TI.item_no = TIL.item_no
            AND TIL.language_code = in_language_code
    LEFT JOIN combined_categories CC
            ON CC.item_no = TI.item_no
    LEFT JOIN t_lot_detail TLD
            ON TLD.item_no = TIL.item_no
    LEFT JOIN t_exhibition_item TEI
            ON TLD.lot_no = TEI.lot_no
            AND (in_exhibition_no IS NULL OR TEI.exhibition_no = in_exhibition_no)
    WHERE TI.status IN (0, 1, 2)
      AND TI.delete_flag = 0
      AND (in_exhibition_no IS NULL OR TEI.exhibition_no = in_exhibition_no)
      AND (in_manage_no IS NULL OR TI.manage_no LIKE '%' || f_escape_string(in_manage_no) || '%')
      AND
      (
        (in_productname IS NULL OR length(in_productname) = 0) OR
        position(LOWER(f_escape_string(in_productname)) IN LOWER(COALESCE(TIL.free_field->>'product_name', ''))) > 0
      )
      AND
      (
        -- When in_category is "その他", exclude categories listed in all_category_constants
        (in_category = 'ct11'
          AND NOT EXISTS (
            SELECT 1 FROM all_category_constants acc
            WHERE
              position((acc.value1) IN (array_to_string(CC.category_id, ''))) > 0
        ))

        OR (-- Handle other in_category values
          in_category IS NOT NULL AND length(in_category) > 0 AND
          position(LOWER(f_escape_string(in_category)) IN LOWER(array_to_string(CC.category_id, ''))) > 0
        )

        OR( -- Handle empty in_category values
            in_category IS NULL OR length(in_category) = 0
        )
      )
      AND
      (
        (in_start_datetime_from IS NULL OR TEI.start_datetime::timestamp >= in_start_datetime_from::timestamp)
        AND
        (in_start_datetime_to IS NULL OR TEI.start_datetime::timestamp <= in_start_datetime_to::timestamp)
      )
      AND
      (
        (in_end_datetime_from IS NULL OR TEI.end_datetime::timestamp >= in_end_datetime_from::timestamp)
        AND
        (in_end_datetime_to IS NULL OR TEI.end_datetime::timestamp <= in_end_datetime_to::timestamp)
      )
    GROUP BY TI.manage_no, TI.item_no, TI.recommend_start_datetime
  )
SELECT DISTINCT
    DENSE_RANK() OVER (ORDER BY LC.manage_no) AS row_number
    , LC.item_no
    , LC.manage_no
    , TE.tenant_no
    , CASE WHEN LC.recommend_start_datetime IS NOT NULL THEN true ELSE false END AS recommend_flag
    , L.lot_no
    , L.lot_id
    , LC.localized_json_array::text
    , CASE WHEN LD.order_no IS NULL THEN NULL WHEN LD.order_no = 1 THEN EI.lowest_bid_price ELSE 0 END as lowest_bid_price
    , LC.lowest_bid_quantity
    , CASE WHEN LD.order_no IS NULL THEN NULL WHEN LD.order_no = 1 THEN EI.lowest_bid_accept_price ELSE 0 END as lowest_bid_accept_price
    , LC.lowest_bid_accept_quantity
    , LC.quantity
    , to_char(EI.start_datetime, 'yyyy-MM-dd HH24:MI') start_datetime
    , to_char(EI.end_datetime, 'yyyy-MM-dd HH24:MI') end_datetime
    , EI.cancel_flag
    , EI.bid_count
    , LD.order_no
    , COALESCE(to_char(EI.default_end_datetime, 'yyyy-MM-dd HH24:MI'), NULL) AS default_end_datetime
    , EI.top_price
    , EI.current_price
    , EI.hummer_flag
    , CASE
        WHEN EXISTS (
          SELECT 1 FROM t_exhibition_message TEM2
          WHERE TEM2.exhibition_item_no = EI.exhibition_item_no
            AND TEM2.update_category_id = '2'
            AND TEM2.checked_admin_no IS NULL
        ) THEN true
        ELSE false
      END AS no_read_inquiry
	FROM localized LC
    -- LEFT JOIN t_item I ON I.delete_flag = 0
    LEFT JOIN t_lot_detail LD
      ON LD.item_no = LC.item_no
    LEFT JOIN t_lot L
      ON L.lot_no = LD.lot_no
    LEFT JOIN t_exhibition_item EI
      ON EI.lot_no = L.lot_no
    LEFT JOIN t_exhibition TE
      ON TE.exhibition_no = EI.exhibition_no
    LEFT JOIN t_exhibition_message TEM
      ON TEM.exhibition_item_no = EI.exhibition_item_no
  ORDER BY L.lot_id, LD.order_no, LC.manage_no;

END;

$BODY$;
