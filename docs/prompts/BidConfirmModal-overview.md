# 🧾 BidConfirmModal Overview

Modal component for confirming and submitting auction bids.

```
/auction-side/src/components/common/BidConfirmModal.vue
```

## 📦 Structure

- **Props**:
  `isAscendingAuction` (`boolean`)
- **Model**:
  Uses `v-model="open"` to control visibility
- **Events**:
  Emits `refresh` when a bid is successful
- **Store Integration** (`useBidConfirmStore()`):
  - `showBidConfirm`: Controls modal visibility
  - `data`: Array of exhibition items with bid info
  - `agree`: Tracks agreement to terms
  - `showBidResult`: Toggles bid result view

## 🔄 Flow Summary

1. User clicks **"入札する"** in `StandardAuctionItem.vue`
2. `bidHandle(item)` is called from `useBid()`
3. `bidHandle` validates and calls `showBidConfirmDialog()`
4. `showBidConfirmDialog()` updates store and toggles `showBidConfirm`
5. Modal in `_MyPage.vue` is shown via `v-model="bid.showBidConfirm"`

**Current Implementation Context:**

- BidConfirmModal uses `v-model="open"` for visibility control
- The modal is controlled by `bid.showBidConfirm` from `useBidConfirmStore()`
- Current flow: User clicks "入札する" → `bidHandle()` → `showBidConfirmDialog()` → toggles `bid.showBidConfirm` → should show modal
