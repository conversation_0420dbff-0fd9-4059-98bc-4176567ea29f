# PRの文章生成

このプロンプトは日本語を使用してください。
以下にPRの文章生成の手順を示します。

## 1. GitHub Copilotのモード確認

あなたが現在利用可能なツールに、Git操作系（get_changed_filesなど）が含まれているかどうかを確認してください。

### あなたが現在利用可能なツールにGit操作系（get_changed_filesなど）が含まれていないケース

ユーザーにGitHub CopilotをAgentモードに切り替えることを伝えて処理を中断します。

### あなたが現在利用可能なツールにGit操作系（get_changed_filesなど）が含まれているケース

処理を続行してください。

## 2. デフォルトブランチと現在のブランチの差分を確認

以下のコマンドで差分を確認し、どのような変更が行われたかを把握してください。

```bash
git fetch && \
git remote set-head origin -a && \
git diff origin/feature/demo2...HEAD
```

<!--
以下はコマンドの解説です。
- `git fetch`: リモートの最新情報を取得します（ブランチ、タグなど）
- `git remote set-head origin -a`: リモートのデフォルトブランチ（例: main）を `origin/HEAD` に設定します
- `git diff origin/feature/demo2...HEAD`: 現在のブランチと `feature/demo2` ブランチとの差分を表示します（PR作成用）

-->

## 3. 関連のあるIssueを確認

変更内容に関連するGitHubのIssueがあるかどうかを探します。
関連するIssueが見つかった場合に、次のステップでIssue番号を記述します。

GitHub MCPに以下の内容をリクエストしてください。

```json
{
  "owner": "GMO-MAKESHOP",
  "repo": "saas-mock",
  "per_page": 30,
  "state": "open",
  "sort": "updated",
  "direction": "desc"
}
```

## 4. PR文章を生成

PR文章の生成は次のルールを守ってください。

- ユーザーがコピペできるようMarkdownコードブロックで囲み出力する
- 動作確認内容は今回の変更からいくつかのものを書いてください。
- PR文章は以下の形式に従う（コメントはそのまま）

````markdown
```markdown
# 関連Issue

- #xxx

## 📝 このプルリクエストについて

- 概要:

## 🔧 変更内容

- [x] xxx を追加
- [x] xxx を修正
- [x] xxx を削除

## ✅ チェックリスト

- [x] ビルドが正常に通ることを確認した
- [x] 動作確認内容：
  - [ ] １．っっｘ：
  - [ ] １．っっｘ

## 📷 動作確認またはスクリーンショット

## 💬 補足事項

- 例: XXX
```
````
