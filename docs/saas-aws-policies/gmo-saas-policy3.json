{"Version": "2012-10-17", "Statement": [{"Sid": "AllowPolicyCloudFront", "Effect": "Allow", "Action": ["cloudwatch:TagResource", "cloudwatch:UntagResource", "cloudfront:CreateDistribution", "cloudfront:UpdateDistribution", "cloudfront:DeleteDistribution", "cloudfront:CreateInvalidation", "cloudfront:CreateCloudFrontOriginAccessIdentity", "cloudfront:UpdateCloudFrontOriginAccessIdentity", "cloudfront:DeleteCloudFrontOriginAccessIdentity", "cloudfront:CreateFunction", "cloudfront:UpdateFunction", "cloudfront:DeleteFunction", "cloudfront:CreateCachePolicy", "cloudfront:UpdateCachePolicy", "cloudfront:DeleteCachePolicy", "cloudfront:CreateOriginRequestPolicy", "cloudfront:UpdateOriginRequestPolicy", "cloudfront:DeleteOriginRequestPolicy", "cloudfront:CreateResponseHeadersPolicy", "cloudfront:UpdateResponseHeadersPolicy", "cloudfront:DeleteResponseHeadersPolicy", "cloudfront:TagResource", "cloudfront:UntagResource", "cloudfront:AssociateAlias", "cloudfront:DisassociateAlias", "cloudwatch:PutMetricAlarm", "cloudwatch:DeleteAlarms", "cloudwatch:PutMetricStream", "cloudwatch:PutMetricData"], "Resource": ["arn:aws:cloudfront::337607240739:distribution/saas-dev-*", "arn:aws:cloudfront::337607240739:origin-access-identity/saas-dev-*", "arn:aws:cloudfront::337607240739:function/saas-dev-*", "arn:aws:cloudfront::337607240739:cache-policy/saas-dev-*", "arn:aws:cloudfront::337607240739:origin-request-policy/saas-dev-*", "arn:aws:cloudfront::337607240739:response-headers-policy/saas-dev-*", "arn:aws:cloudwatch:ap-northeast-1:337607240739:alarm:saas-dev-*", "arn:aws:cloudwatch:ap-northeast-1:337607240739:metric-stream/saas-dev-*"]}, {"Sid": "AllowPolicySecretsmanager", "Effect": "Allow", "Action": ["secretsmanager:<PERSON><PERSON><PERSON><PERSON><PERSON>", "secretsmanager:PutSecretV<PERSON>ue", "secretsmanager:UpdateSecret", "secretsmanager:TagResource", "secretsmanager:DeleteSecret", "secretsmanager:UntagResource"], "Resource": ["arn:aws:secretsmanager:ap-northeast-1:337607240739:secret:saas-dev-*"]}, {"Sid": "AllowPolicyAcm", "Effect": "Allow", "Action": ["acm:RequestCertificate", "acm:AddTagsToCertificate", "acm:DeleteCertificate", "acm:ResendValidationEmail", "acm:ImportCertificate", "acm:ExportCertificate"], "Resource": ["arn:aws:acm:ap-northeast-1:337607240739:certificate/*"]}, {"Sid": "AllowPolicyRoute53", "Effect": "Allow", "Action": ["route53:ChangeResourceRecordSets", "route53:DeleteHostedZone", "route53:CreateHostedZone"], "Resource": ["arn:aws:route53:::hostedzone/*"]}, {"Sid": "AllowPolicyLogs", "Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents", "logs:DeleteLogGroup", "logs:DeleteLogStream", "logs:PutResourcePolicy", "logs:CreateLogDelivery", "logs:CreateDelivery", "logs:PutDestinationPolicy", "logs:PutDestination", "logs:PutRetentionPolicy", "logs:DeleteRetentionPolicy", "logs:TagLogGroup", "logs:UntagLogGroup"], "Resource": ["arn:aws:logs:ap-northeast-1:337607240739:log-group:saas-dev-*", "arn:aws:logs:ap-northeast-1:337607240739:log-stream:saas-dev-*", "arn:aws:logs:ap-northeast-1:337607240739:destination:saas-dev-*"]}, {"Sid": "AllowPolicyEvents", "Effect": "Allow", "Action": ["events:PutRule", "events:PutTargets", "events:RemoveTargets", "events:DeleteRule", "events:PutEvents", "events:EnableRule", "events:DisableRule", "events:TagResource", "events:UntagResource"], "Resource": ["arn:aws:events:ap-northeast-1:337607240739:rule/saas-dev-*"]}, {"Sid": "AllowPolicySns", "Effect": "Allow", "Action": ["sns:CreateTopic", "sns:SetTopicAttributes", "sns:DeleteTopic", "sns:Subscribe", "sns:Unsubscribe", "sns:Publish", "sns:TagResource", "sns:UntagResource"], "Resource": ["arn:aws:sns:ap-northeast-1:337607240739:saas-dev-*"]}]}