{"Version": "2012-10-17", "Statement": [{"Sid": "AllowPolicyEc2", "Effect": "Allow", "Action": ["ec2:CreateNatGateway", "ec2:CreateRoute", "ec2:CreateSecurityGroup", "ec2:AuthorizeSecurityGroupIngress", "ec2:AuthorizeSecurityGroupEgress", "ec2:CreateInternetGateway", "ec2:AttachInternetGateway", "ec2:RunInstances", "ec2:TerminateInstances", "ec2:CreateTags", "ec2:ImportKeyPair", "ec2:AllocateAddress", "ec2:CreateVpc", "ec2:DeleteVpc", "ec2:DeleteKeyPair", "ec2:C<PERSON>KeyPair", "ec2:ImportKeyPair", "ec2:CreateNatGateway", "ec2:CreateNetworkAcl", "ec2:CreateRoute", "ec2:CreateSubnet", "ec2:CreateVolume", "ec2:CreateVpc", "ec2:DeleteSubnet", "ec2:DeleteVolume", "ec2:DeleteVpc", "ec2:ReleaseAddress", "ec2:CreateRouteTable", "ec2:AssociateRouteTable", "ec2:ModifySubnetAttribute", "ec2:AssociateRouteTable", "ec2:AssociateRouteTable", "ec2:RevokeSecurityGroupEgress", "ec2:AssociateSubnetCidrBlock", "ec2:CreateDefaultSubnet", "ec2:AttachInternetGateway", "ec2:CreateEgressOnlyInternetGateway", "ec2:DeleteEgressOnlyInternetGateway", "ec2:DeleteInternetGateway", "ec2:DetachInternetGateway", "ec2:ApplySecurityGroupsToClientVpnTargetNetwork", "ec2:AssociateSecurityGroupVpc", "ec2:AuthorizeSecurityGroupEgress", "ec2:AuthorizeSecurityGroupIngress", "ec2:DeleteSecurityGroup", "ec2:DisassociateSecurityGroupVpc", "ec2:ModifySecurityGroupRules", "ec2:RevokeSecurityGroupIngress", "ec2:UpdateSecurityGroupRuleDescriptionsEgress", "ec2:UpdateSecurityGroupRuleDescriptionsIngress", "ec2:DeleteRoute", "ec2:DisassociateRouteTable", "ec2:DeleteNatGateway", "ec2:DeleteRouteTable", "ec2:DisassociateAddress", "ec2:DeleteSecurityGroup", "ec2:<PERSON><PERSON><PERSON><PERSON>", "ec2:CreateNetworkInterface", "ec2:DeleteNetworkInterface"], "Resource": ["arn:aws:ec2:ap-northeast-1:337607240739:vpc/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:subnet/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:security-group/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:route-table/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:internet-gateway/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:natgateway/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:instance/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:volume/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:network-interface/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:key-pair/saas-dev-*", "arn:aws:ec2:ap-northeast-1:337607240739:elastic-ip/*"]}, {"Sid": "AllowPolicyWafv2", "Effect": "Allow", "Action": ["wafv2:CreateIPSet", "wafv2:CreateWebACL", "wafv2:TagResource", "wafv2:DeleteIPSet", "wafv2:DeleteWebACL", "wafv2:PutLoggingConfiguration", "wafv2:AssociateWebACL", "wafv2:CreateRegexPatternSet", "wafv2:CreateRuleGroup", "wafv2:DeleteRegexPatternSet", "wafv2:DeleteRuleGroup", "wafv2:PutFirewallManagerRuleGroups", "wafv2:PutManagedRuleSetVersions", "wafv2:UpdateRegexPatternSet", "wafv2:UpdateRuleGroup", "wafv2:DeleteLoggingConfiguration", "wafv2:UpdateWebACL"], "Resource": ["arn:aws:wafv2:ap-northeast-1:337607240739:regional/webacl/saas-dev-*", "arn:aws:wafv2:ap-northeast-1:337607240739:regional/ipset/saas-dev-*", "arn:aws:wafv2:ap-northeast-1:337607240739:regional/regexpatternset/saas-dev-*", "arn:aws:wafv2:ap-northeast-1:337607240739:regional/rulegroup/saas-dev-*", "arn:aws:wafv2:global:337607240739:global/webacl/saas-dev-*", "arn:aws:wafv2:global:337607240739:global/ipset/saas-dev-*"]}, {"Sid": "AllowPolicySes", "Effect": "Allow", "Action": ["ses:SendEmail", "ses:SendRawEmail", "ses:VerifyDomainIdentity", "ses:VerifyEmailIdentity", "ses:DeleteIdentity"], "Resource": ["arn:aws:ses:ap-northeast-1:337607240739:identity/*"]}, {"Sid": "AllowPolicyCognito", "Effect": "Allow", "Action": ["cognito-idp:CreateUserPool", "cognito-idp:UpdateUserPool", "cognito-idp:CreateUserPoolClient", "cognito-identity:CreateIdentityPool", "cognito-identity:SetIdentityPoolRoles", "cognito-idp:CreateGroup", "cognito-idp:CreateUserPoolDomain", "cognito-idp:DeleteUserPool", "cognito-idp:DeleteUserPoolClient", "cognito-idp:DeleteGroup", "cognito-idp:DeleteUserPoolDomain", "cognito-idp:AdminCreateUser", "cognito-idp:AdminDeleteUser", "cognito-idp:AdminSetUserPassword", "cognito-idp:AdminAddUserToGroup", "cognito-idp:AdminRemoveUserFromGroup", "cognito-idp:AdminDeleteUserAttributes", "cognito-idp:AdminSetUserSettings", "cognito-idp:AdminUpdateUserAttributes", "cognito-idp:AdminUpdateUserAttributes", "cognito-idp:AdminUpdateUserAttributes"], "Resource": ["arn:aws:cognito-idp:ap-northeast-1:337607240739:userpool/saas-dev-*", "arn:aws:cognito-identity:ap-northeast-1:337607240739:identitypool/saas-dev-*"]}, {"Sid": "AllowPolicyOther", "Effect": "Allow", "Action": ["ec2:Describe*", "ec2:Get*", "ec2:List*", "iam:Get*", "iam:List*", "lambda:Get*", "lambda:List*", "cognito-idp:Describe*", "cognito-idp:Get*", "cognito-idp:List*", "s3:List*", "s3:Get*", "rds:Describe*", "rds:List*", "cloudfront:Describe*", "cloudfront:Get*", "cloudfront:List*", "secretsmanager:Describe*", "secretsmanager:Get*", "secretsmanager:List*", "acm:Describe*", "acm:Get*", "acm:List*", "route53:List*", "route53:Get*", "logs:Describe*", "logs:Get*", "logs:List*", "cloudwatch:List*", "cloudwatch:Get*", "cloudwatch:Describe*", "events:Describe*", "events:List*", "sns:Get*", "sns:List*", "wafv2:Describe*", "wafv2:Get*", "wafv2:List*", "ses:Describe*", "ses:Get*", "ses:List*"], "Resource": "*"}]}