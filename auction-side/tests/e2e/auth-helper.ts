import { Page } from '@playwright/test'

/**
 * Authentication helper functions for E2E tests
 * Handles AWS Cognito authentication flows
 */

export interface TestUser {
  email: string
  password: string
  name?: string
}

export const TEST_USERS = {
  // Test user credentials (these should be test accounts only)
  validUser: {
    email: '<EMAIL>',
    password: 'TestPassword123',
    name: 'Test User'
  },
  invalidUser: {
    email: '<EMAIL>',
    password: 'wrongpassword'
  }
}

/**
 * Login with test credentials
 */
export async function loginUser(page: Page, user: TestUser): Promise<boolean> {
  try {
    // Navigate to login page
    await page.goto('/login')
    await page.waitForLoadState('networkidle')

    // Fill in login form
    const emailInput = page.locator('input[type="email"], input[name="email"]')
    const passwordInput = page.locator('input[type="password"], input[name="password"]')
    const loginButton = page.locator('button[type="submit"], .login-button, button:has-text("ログイン")')

    await emailInput.fill(user.email)
    await passwordInput.fill(user.password)
    await loginButton.click()

    // Wait for navigation or success indicator
    await page.waitForTimeout(2000)

    // Check if login was successful by looking for user indicators
    const isLoggedIn = await page.locator('.user-menu, .logout-button, text=ログアウト').isVisible()
    
    return isLoggedIn
  } catch (error) {
    console.error('Login failed:', error)
    return false
  }
}

/**
 * Logout current user
 */
export async function logoutUser(page: Page): Promise<void> {
  try {
    // Look for logout button or user menu
    const logoutButton = page.locator('.logout-button, button:has-text("ログアウト")')
    const userMenu = page.locator('.user-menu')

    if (await userMenu.isVisible()) {
      await userMenu.click()
      await page.locator('text=ログアウト').click()
    } else if (await logoutButton.isVisible()) {
      await logoutButton.click()
    }

    // Confirm logout if there's a confirmation dialog
    const confirmButton = page.locator('button:has-text("ログアウト"), button:has-text("確認")')
    if (await confirmButton.isVisible()) {
      await confirmButton.click()
    }

    await page.waitForTimeout(1000)
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

/**
 * Check if user is currently authenticated
 */
export async function isUserAuthenticated(page: Page): Promise<boolean> {
  try {
    // Check for authentication indicators
    const authIndicators = [
      '.user-menu',
      '.logout-button', 
      'text=ログアウト',
      '.authenticated-user',
      '[data-authenticated="true"]'
    ]

    for (const indicator of authIndicators) {
      if (await page.locator(indicator).isVisible()) {
        return true
      }
    }

    return false
  } catch (error) {
    console.error('Auth check failed:', error)
    return false
  }
}

/**
 * Wait for authentication state to change
 */
export async function waitForAuthChange(page: Page, expectedState: boolean, timeout = 5000): Promise<boolean> {
  const startTime = Date.now()
  
  while (Date.now() - startTime < timeout) {
    const currentState = await isUserAuthenticated(page)
    if (currentState === expectedState) {
      return true
    }
    await page.waitForTimeout(500)
  }
  
  return false
}

/**
 * Mock authentication for testing (when using mock API)
 */
export async function mockAuthentication(page: Page, authenticated = true): Promise<void> {
  // Set localStorage or sessionStorage to mock authentication state
  await page.evaluate((auth) => {
    if (auth) {
      localStorage.setItem('isAuthenticated', 'true')
      localStorage.setItem('user', JSON.stringify({
        email: '<EMAIL>',
        name: 'Test User',
        id: 'test-user-id'
      }))
    } else {
      localStorage.removeItem('isAuthenticated')
      localStorage.removeItem('user')
    }
  }, authenticated)

  // Reload page to apply authentication state
  await page.reload()
  await page.waitForLoadState('networkidle')
}

/**
 * Clear all authentication data
 */
export async function clearAuthData(page: Page): Promise<void> {
  await page.evaluate(() => {
    // Clear localStorage
    localStorage.removeItem('isAuthenticated')
    localStorage.removeItem('user')
    localStorage.removeItem('authToken')
    localStorage.removeItem('refreshToken')
    
    // Clear sessionStorage
    sessionStorage.removeItem('isAuthenticated')
    sessionStorage.removeItem('user')
    sessionStorage.removeItem('authToken')
    
    // Clear cookies
    document.cookie.split(";").forEach(cookie => {
      const eqPos = cookie.indexOf("=")
      const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
      document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
    })
  })
}

/**
 * Setup test environment for authentication tests
 */
export async function setupAuthTest(page: Page): Promise<void> {
  // Clear any existing auth data
  await clearAuthData(page)
  
  // Navigate to home page
  await page.goto('/')
  await page.waitForLoadState('networkidle')
}
