export * from './eventBus.js'
import {useMediaQuery} from '@vueuse/core'
import {computed} from 'vue'
import {usePrevRouteStore} from '../stores/prev-route.js'

const breakpoints = {
  xs: 576, // X-Small: <576px
  sm: 767, // Small: ≥576px
  md: 992, // Medium: ≥768px
  lg: 1200, // Large: ≥992px
  xl: 1400, // Extra large: ≥1200px
  xxl: Infinity, // Extra extra large: ≥1400px
}
export const isMobile = useMediaQuery(`(max-width: ${breakpoints.xs}px)`)
export const isLowerThanSM = useMediaQuery(`(max-width: ${breakpoints.sm}px)`)
export const isTablet = useMediaQuery(`(min-width: ${breakpoints.sm}px) and (max-width: ${breakpoints.md - 1}px)`)
export const isPC = useMediaQuery(`(min-width: ${breakpoints.md}px)`)

// const minPCWith = 992
const generateFullPath = path => {
  const baseUrl = window.location.origin
  return `${baseUrl}${path}`
}

/**
 * Navigates to the specified path based on the device type.
 * - On PC (screen width greater than `minPCWith`), it opens the path in a new tab.
 * - On smartphones or smaller devices, it redirects to the path in the same tab.
 * @param {string} path - The relative path to navigate to (e.g., `/bids`).
 * @param {Function} goToPath - The function to navigate to a path on smaller devices.
 * @param route
 */
export const conditionalNavigate = (path, goToPath, route) => {
  const fullPath = generateFullPath(path)
  const prevRouteStore = usePrevRouteStore()
  if (isPC.value) {
    prevRouteStore.setPrevRoute(route)
    window.open(fullPath, '_blank')
  } else if (typeof goToPath === 'function') {
    goToPath(path)
  }
}

/**
 * Returns a computed label for the auction status.
 * @param {object} detailRef - The auction detail ref or reactive object.
 * @param detail
 * @param {Function} t - The translation function.
 * @param page
 * @returns {object} - A computed value containing the auction status label.
 */
export const getAuctionStatusLabel = (detailRef, t, page) => {
  return computed(() => {
    const detail = detailRef?.value
    const endDate = new Date(detail.bid_status.end_datetime)
    const presentTime = new Date()

    if (!detail || !detail.bid_status) {
      return ''
    }
    if (detail.bid_status.is_cancel === true) {
      return t('BID_STATUS_CANCEL')
    }
    if (!detail.bid_status.started) {
      return t('BID_STATUS_NOT_START_YET')
    }
    if (detail.sold_out || presentTime > endDate) {
      return t('BID_STATUS_ENDED')
    }
    if (page !== 'detail' && detail.bid_status.extending) {
      return t('BID_STATUS_EXTENDING')
    }
    return t('BID_STATUS_INPROGRESS')
  })
}

export const calculateRemainingTime = (endDatetime, t, updateStatusCallback) => {
  const now = new Date().getTime()
  const endTime = new Date(endDatetime).getTime()

  if (endTime > now) {
    const diff = Math.floor((endTime - now) / 1000)
    const minutes = Math.floor(diff / 60)
    const seconds = diff % 60

    if (minutes < 5) {
      console.log('⛳️ remainning time:  ', `${minutes}m ${seconds}s`)
    }
    return `${minutes}m ${seconds}s`
  } else {
    if (updateStatusCallback) updateStatusCallback(t('BID_STATUS_ENDED'))
    return 'Auction Ended'
  }
}

export const startCountdown = (endDatetime, t, updateStatusCallback) => {
  let countdownTimer = null

  const updateCountdown = () => {
    const remainingTime = calculateRemainingTime(endDatetime, t, updateStatusCallback)
    // updateTimeCallback(remainingTime);

    if (remainingTime === 'Auction Ended') {
      clearInterval(countdownTimer)
    }
  }

  // Start immediately
  updateCountdown()

  // Update every second
  countdownTimer = setInterval(updateCountdown, 1000)

  return countdownTimer
}
