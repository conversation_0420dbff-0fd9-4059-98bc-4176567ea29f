<script setup>
  import {ref} from 'vue'
  import {Carousel, Navigation, Pagination, Slide} from 'vue3-carousel'
  import 'vue3-carousel/dist/carousel.css'

  const props = defineProps(['images', 'sold_out'])

  // 画像処理
  const showCarousel = ref(false)
  const currentImg = ref(0)
  /**
   *
   * @param idx
   */
  function setCurrentImg(idx) {
    currentImg.value = idx
    showCarousel.value = true
  }
</script>
<template>
  <div class="item_d-main-visual">
    <div class="slider_wrap">
      <carousel id="gallery" :items-to-show="1" :wrapAround="true" v-model="currentImg">
        <slide v-for="(image, idx) in images ?? []" :key="idx" class="slide-item">
          <p v-if="sold_out" class="status_soldout" @click="setCurrentImg(idx)"></p>
          <a data-size="600x600" @click="setCurrentImg(idx)">
            <img :src="image" alt="" />
          </a>
        </slide>

        <template #addons>
          <Navigation />
          <Pagination />
        </template>
      </carousel>

      <Carousel
        id="thumbnails"
        :items-to-show="7"
        :itemsToScroll="1"
        :wrap-around="false"
        snapAlign="start"
        v-model="currentImg"
        ref="carousel"
        :breakpoints="{
          // 700px and up
          700: {
            itemsToShow: 4,
            snapAlign: 'start',
          },
          // 1024 and up
          1024: {
            itemsToShow: 7,
            snapAlign: 'start',
          },
        }"
      >
        <Slide v-for="(image, idx) in images ?? []" :key="idx">
          <img class="thumbnail-item" style="display: block" :src="image" alt="" @click="currentImg = idx" />
        </Slide>
        <template #addons="{slidesCount}">
          <Navigation v-if="slidesCount > 1" />
        </template>
      </Carousel>
    </div>

    <v-dialog v-model="showCarousel" max-width="900">
      <v-card>
        <v-card-item>
          <v-carousel hide-delimiters v-model="currentImg">
            <template v-slot:prev="{props}">
              <button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)" @click="props.onClick"></button>
            </template>
            <template v-slot:next="{props}"
              ><button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)" @click="props.onClick"></button>
            </template>
            <v-carousel-item v-for="(image, idx) in images ?? []" :key="idx" :src="image" :value="idx"></v-carousel-item>
          </v-carousel>
        </v-card-item>
      </v-card>
    </v-dialog>
  </div>
</template>

<style scoped>
  .slider_wrap {
    width: 100%;
  }
  #main #item-data .item_d-main .item_d-main-visual {
    width: calc(100% - 497px);
    padding: 0 2rem;
  }
  @media screen and (max-width: 1080px) {
    #main #item-data .item_d-main .item_d-main-visual {
      width: calc(100% - 427px);
    }
  }
  @media screen and (max-width: 767px) {
    #main #item-data .item_d-main .item_d-main-visual {
      width: 100%;
      padding: 0;
    }
  }

  #gallery {
    min-height: 250px;
  }

  #gallery >>> .carousel__prev {
    left: -25px !important;
  }
  #gallery >>> .carousel__next {
    right: -25px !important;
  }

  #thumbnails {
    margin-top: 5px;
  }
  #thumbnails >>> .carousel__slide {
    padding: 5px;
  }

  .item_d-main-visual .carousel__track {
    transform-style: preserve-3d;
    transform: translateX(0px);
  }

  .item_d-main-visual .carousel__slide {
    opacity: 0.9;
    transform: rotateY(-20deg) scale(0.9);
  }

  .item_d-main-visual .carousel__slide--active ~ .carousel__slide {
    transform: rotateY(20deg) scale(0.9);

    height: 80px;
    margin: 0 3px;
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }

  #thumbnails >>> .carousel__slide--prev {
    opacity: 1;
    transform: rotateY(-10deg) scale(0.95);
  }

  #thumbnails >>> .carousel__slide--next {
    opacity: 1;
    transform: rotateY(10deg) scale(0.95);
  }

  #thumbnails >>> .carousel__slide--active {
    opacity: 1;
    transform: rotateY(0) scale(1.1);
  }

  #thumbnails >>> .carousel__prev {
    left: -30px !important;
    height: 100% !important;
    border-radius: 10px 0 0 10px;
    z-index: 1;
  }
  #thumbnails >>> .carousel__next {
    right: -30px !important;
    height: 100% !important;
    border-radius: 0 10px 10px 0;
    z-index: 1;
  }
  #thumbnails .carousel__track .carousel__slide--visible {
    height: 80px;
    margin: 0 3px;
    overflow: hidden;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
  }
  .status_soldout {
    width: 80%;
    height: auto;
    display: block;
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
  }
</style>
