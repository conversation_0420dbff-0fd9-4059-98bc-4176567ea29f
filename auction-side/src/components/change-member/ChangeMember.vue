<script setup>
  import {defineAsyncComponent, onBeforeMount, reactive, ref, watch} from 'vue'
  import {useRouter} from 'vue-router'
  import {useLocale} from 'vuetify'
  import {convertFullWidthToHalfWidth, scrollToTop} from '../../composables/common'
  import useApi from '../../composables/useApi'
  import {useAuthStore} from '../../stores/auth'
  import {useChangeMemberStore} from '../../stores/change-member'
  import {useMemberStore} from '../../stores/member-info'
  import {useMessageDialogStore} from '../../stores/messag-dialog'

  const EntryForm = defineAsyncComponent(() => import(/* webpackChunkName: "EntryForm" */ './EntryForm.vue'))
  const ConfirmData = defineAsyncComponent(() => import(/* webpackChunkName: "ConfirmData" */ './ConfirmData.vue'))

  const router = useRouter()
  const {t} = useLocale()
  const dialog = useMessageDialogStore()
  const auth = useAuthStore()
  const member = useMemberStore()
  const {registInputs, resetPasswords} = useChangeMemberStore()
  const {apiExecute, parseHtmlResponseError} = useApi()

  const step = ref(1)
  const showError = ref(false)
  const constants = ref([])

  const errorMsg = reactive({
    country: null,
    ceoName: null,
    ceoNameKana: null,
    ceoBirthday: null,
    companyName: null,
    companyNameKana: null,
    companyAddress: null,
    establishmentDate: null,
    companyHp: null,
    businessContent: null,
    invoiceNo: null,
    tel: null,
    antiquePermitNo: null,
    antiquePermitDate: null,
    antiquePermitCommission: null,
    memberLastName: null,
    memberName: null,
    whatsApp: null,
    wechat: null,
    memo: null,
    email: null,
    emailConfirm: null,
    password: null,
    passwordConfirm: null,
    emailDuplicated: null,
  })

  const nextStep = () => {
    step.value += 1
  }
  const prevStep = () => {
    step.value -= 1
  }

  const fullWidthToHalfWidthConversion = params => {
    const registerData = {...params}
    Object.keys(registerData).map(key => {
      if (key === 'antiquePermitNo' || key === 'password' || key === 'passwordConfirm' || key === 'tel') {
        registerData[key] = convertFullWidthToHalfWidth(registerData[key])
      }
    })
    return registerData
  }

  const sendConfirmRequest = async params => {
    const registerData = fullWidthToHalfWidthConversion(params)
    const reqParams = {registerData, validateFlag: true}
    try {
      Object.keys(errorMsg).map(key => {
        errorMsg[key] = null
      })
      await apiExecute('private/change-member-info', reqParams)
      scrollToTop()
      if (!Object.values(errorMsg).some(val => val)) {
        nextStep()
      }
    } catch (error) {
      const err = parseHtmlResponseError(error)
      scrollToTop()
      Object.keys(err).map(x => {
        if (err[x]) {
          errorMsg[x] = err[x]
        }
      })
    }
  }

  const sendRequest = async params => {
    const registerData = fullWidthToHalfWidthConversion(params)
    const reqParams = {registerData}
    try {
      await apiExecute('private/change-member-info', reqParams)
      scrollToTop()
      dialog.setShowMessage(t('register.form.changeFinish'), {
        name: 'change-member-info',
        showOkButton: true,
        showCloseButton: false,
      })
      showError.value = false
    } catch (error) {
      console.log(error)
      scrollToTop()
      dialog.setShowMessage(t('common.error'), {
        showOkButton: true,
        showCloseButton: false,
      })
    }
  }

  const getConstants = async () => {
    await apiExecute('private/get-change-info-constants')
      .then(res => {
        constants.value = res.constants
        member.setMemberInfo(res)
        registInputs.map(input => {
          input.value = res.member[input.item] ?? input.value
        })
        // Refresh nickname
        auth.setNickname(res.member.nickname)
      })
      .catch(error => console.log(error))
  }

  const showWithdrawalConfirmDialog = () => {
    dialog.setShowMessage(t('register.form.withdrawConfirm'), {
      name: 'withdraw-member-open',
      showConfirmButton: true,
    })
  }

  watch(
    () => dialog.showMessageDialog,
    newVal => {
      switch (dialog.dialogName) {
        case 'change-member-info':
          if (!newVal && !showError.value) {
            prevStep()
          }
          break
        case 'withdraw-member-finish':
          if (!newVal && !showError.value) {
            auth.handleLogout()
          }
          break
        default:
          break
      }
    }
  )

  watch(
    () => dialog.clickedConfirm,
    async newVal => {
      if (newVal) {
        switch (dialog.dialogName) {
          case 'withdraw-member-open':
            dialog.handleClose()
            await apiExecute('private/withdraw-member')
              .then(() => {
                dialog.setShowMessage(t('register.form.withdrawFinish'), {
                  name: 'withdraw-member-finish',
                  showOkButton: true,
                  showCloseButton: false,
                })
                showError.value = false
              })
              .catch(error => {
                const err = parseHtmlResponseError(error)
                dialog.setShowMessage(err.message, {isErr: true})
              })
            break
          default:
            break
        }
      }
    }
  )

  onBeforeMount(async () => {
    await getConstants()
    resetPasswords()
  })
  const emailLangList = [
    {title: 'EN', value: 'en'},
    {title: 'JA', value: 'ja'},
  ]

  const handleErrorMsg = ({field, message}) => {
    errorMsg[field] = message
  }
</script>
<template>
  <div class="container">
    <EntryForm
      v-if="step === 1"
      :constants="constants"
      :errorMsg="errorMsg"
      @confirm-inputs="sendConfirmRequest"
      @withdrawal="showWithdrawalConfirmDialog"
      @update:errorMsg="handleErrorMsg"
      :emailLangOptions="emailLangList"
    />
    <ConfirmData
      v-else-if="step === 2"
      :constants="constants"
      :errorMsg="errorMsg"
      @back="prevStep"
      @regist-member="sendRequest"
      :emailLangOptions="emailLangList"
    />
  </div>
</template>
