<script setup>
  import {formatDateStringLocale} from '@/composables/common'
  import {computed, defineProps} from 'vue'
  import {useLocale} from 'vuetify'

  const props = defineProps(['exhibitionInfo'])

  const {t: translate} = useLocale()

  // Japan time
  const startDateTimeJapan = computed(() => {
    return formatDateStringLocale({
      dateString: props.exhibitionInfo?.start_datetime,
      timeZone: 'Asia/Tokyo',
      dateFormat: translate('common.dateFormat'),
    })
  })
  const endDateTimeJapan = computed(() => {
    return formatDateStringLocale({
      dateString: props.exhibitionInfo?.end_datetime,
      timeZone: 'Asia/Tokyo',
      dateFormat: translate('common.dateFormat'),
    })
  })

  // Dubai time
  const startDateTimeDubai = computed(() => {
    return formatDateStringLocale({
      dateString: props.exhibitionInfo?.start_datetime,
      timeZone: 'Asia/Dubai',
      dateFormat: translate('common.dateFormat'),
    })
  })
  const endDateTimeDubai = computed(() => {
    return formatDateStringLocale({
      dateString: props.exhibitionInfo?.end_datetime,
      timeZone: 'Asia/Dubai',
      dateFormat: translate('common.dateFormat'),
    })
  })

  // Hong Kong time
  const startDateTimeHongKong = computed(() => {
    return formatDateStringLocale({
      dateString: props.exhibitionInfo?.start_datetime,
      timeZone: 'Asia/Hong_Kong',
      dateFormat: translate('common.dateFormat'),
    })
  })
  const endDateTimeHongKong = computed(() => {
    return formatDateStringLocale({
      dateString: props.exhibitionInfo?.end_datetime,
      timeZone: 'Asia/Hong_Kong',
      dateFormat: translate('common.dateFormat'),
    })
  })
</script>
<template>
  <div class="auction-header">
    <div class="ttl-auction">
      {{ exhibitionInfo?.exhibition_name }}
      <span>{{ exhibitionInfo?.auction_classification === '1' ? translate('CLASSIFICATION_ASCENDING') : translate('CLASSIFICATION_SEALED') }} </span>
    </div>
    <div class="schedule">
      <div class="sch-row">
        <div class="country">
          <span class="wic">{{ translate('common.japan') }}</span>
        </div>
        <div class="cont">
          <span class="date">{{ startDateTimeJapan.datePart }}</span
          ><span class="time">{{ startDateTimeJapan.timePart }}</span>
          <span class="symbol">〜</span>
          <span class="date">{{ endDateTimeJapan.datePart }}</span
          ><span class="time">{{ endDateTimeJapan.timePart }}</span>
        </div>
      </div>
      <div class="sch-row">
        <div class="country">
          <span class="wic">{{ translate('common.dubai') }}</span>
        </div>
        <div class="cont">
          <span class="date">{{ startDateTimeDubai.datePart }}</span
          ><span class="time">{{ startDateTimeDubai.timePart }}</span>
          <span class="symbol">〜</span>
          <span class="date">{{ endDateTimeDubai.datePart }}</span
          ><span class="time">{{ endDateTimeDubai.timePart }}</span>
        </div>
      </div>
      <div class="sch-row">
        <div class="country">
          <span class="wic">{{ translate('common.hongkong') }}</span>
        </div>
        <div class="cont">
          <span class="date">{{ startDateTimeHongKong.datePart }}</span
          ><span class="time">{{ startDateTimeHongKong.timePart }}</span>
          <span class="symbol">〜</span>
          <span class="date">{{ endDateTimeHongKong.datePart }}</span
          ><span class="time">{{ endDateTimeHongKong.timePart }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
