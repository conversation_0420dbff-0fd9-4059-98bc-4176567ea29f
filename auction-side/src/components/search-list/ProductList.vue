<!-- <script setup>
  TODO: 使わないと思います。
  import {defineAsyncComponent, defineEmits, defineProps} from 'vue'
  import {useRoute} from 'vue-router'
  import {PATH_NAME} from '../../defined/const'
  import BidAuction from './bid/BidAuction.vue'

  const BidHistoryList = defineAsyncComponent(() => import(/* WebpackChunkName: "BidHistoryList" */ './bid-history/AuctionList.vue'))
  const AllBidHistoryList = defineAsyncComponent(() => import(/* WebpackChunkName: "AllBidHistoryList" */ './bid-history-all/AuctionList.vue'))
  const TopAuctionList = defineAsyncComponent(() => import(/* WebpackChunkName: "TopAuctionList" */ './top/AuctionList.vue'))
  const FavoriteAuction = defineAsyncComponent(() => import(/* WebpackChunkName: "FavoriteAuction" */ './favorite/FavoriteAuction.vue'))

  const emit = defineEmits(['refresh'])

  defineProps(['exhibitionInfo', 'productList'])

  const route = useRoute()

  const refresh = () => {
    emit('refresh')
  }
</script>
<template>
  <div class="auction-conteiner">
    <template v-if="route.path === PATH_NAME.TOP">
      <TopAuctionList :productList="productList" :exhibitionInfo="exhibitionInfo" />
    </template>
    <template v-else-if="route.path === PATH_NAME.FAVORITES">
      <FavoriteAuction :productList="productList" :exhibitionInfo="exhibitionInfo" @refresh="refresh" />
    </template>
    <template v-else-if="route.path === PATH_NAME.BIDS">
      <BidAuction :productList="productList" :exhibitionInfo="exhibitionInfo" @refresh="refresh" />
    </template>
    <template v-else-if="route.path === PATH_NAME.BID_HISTORY">
      <BidHistoryList :productList="productList" :exhibitionInfo="exhibitionInfo" />
    </template>
    <template v-else-if="route.path === PATH_NAME.BID_HISTORY_ALL">
      <AllBidHistoryList :productList="productList" :exhibitionInfo="exhibitionInfo" />
    </template>
  </div>
</template> -->
