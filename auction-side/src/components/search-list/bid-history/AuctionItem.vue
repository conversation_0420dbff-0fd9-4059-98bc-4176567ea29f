<script setup>
  import {format} from 'date-fns'
  import {defineProps} from 'vue'
  import {useLocale} from 'vuetify'
  import {localeString2Number, priceLocaleString} from '../../../composables/common'
  import {usePrevRouteStore} from '../../../stores/prev-route'
  import {conditionalNavigate, isLowerThanSM} from '../../../utils'
  defineProps(['item', 'isAscendingAuction'])

  const {t} = useLocale()
  const {goToPath} = usePrevRouteStore()
</script>
<template>
  <tr class="bid-history-sp-and-pc">
    <!-- 落札日 -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'date-bid'">
      <div v-show="isLowerThanSM">{{ t('bidHistory.endDate') }}：</div>
      {{ format(item.end_datetime, 'yyyy/MM/dd') }}
    </td>

    <!-- 商品名 -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'itemname'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.productName') }}：</div>
      <a @click="conditionalNavigate(item.link, goToPath, $route)" class="cursor-pointer">
        {{ item.free_field?.product_name }}
      </a>
    </td>

    <!-- SIM -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'sim'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.sim') }}：</div>
      {{ item.free_field?.sim }}
    </td>

    <!-- 容量 -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'capacity'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.capacity') }}：</div>
      {{ item.free_field?.capacity }}
    </td>

    <!-- 色 -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'color'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.color') }}：</div>
      {{ item.free_field?.color }}
    </td>

    <!-- グレード -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'grade'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.rank') }}：</div>
      {{ item.free_field?.rank }}
    </td>

    <!-- 落札単価 -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'grade test22324'">
      <div v-show="isLowerThanSM">{{ isAscendingAuction ? t('bidHistory.bidSuccessPrice') : t('bidHistory.bidSuccessUnitPrice') }}：</div>
      <div class="pc-text-center">
        <span class="unit">$</span>
        <span>
          {{ priceLocaleString(item.bid_success_price || 0) }}
        </span>
      </div>
    </td>

    <!-- 落札数 -->
    <td v-if="!isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'grade'">
      <div v-show="isLowerThanSM">{{ t('bidHistory.bidSuccessQuantity') }}：</div>
      {{ priceLocaleString(item.bid_success_quantity || 0) }}
    </td>

    <!-- 合計金額 -->
    <td v-if="!isAscendingAuction" :class="isLowerThanSM ? 'sp-cell-flex' : 'quantity-min'">
      <div v-show="isLowerThanSM">{{ t('bidHistory.bidTotalPrice') }}：</div>
      {{ priceLocaleString(Number(item.bid_success_price || 0) * localeString2Number(item.bid_success_quantity || 0)) }}
    </td>
  </tr>
</template>

<style lang="css" scoped>
  .sp-cell-flex {
    display: flex !important;
    justify-content: space-between;
  }

  @media screen and (min-width: 993px) {
    .pc-text-center {
      display: flex;
      justify-content: center;
    }
  }
</style>
