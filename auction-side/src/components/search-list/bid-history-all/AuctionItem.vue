<script setup>
  import {format} from 'date-fns'
  import {defineProps} from 'vue'
  import {useLocale} from 'vuetify'
  import {priceLocaleString} from '../../../composables/common'
  import {usePrevRouteStore} from '../../../stores/prev-route'
  import {conditionalNavigate, isLowerThanSM} from '../../../utils'

  defineProps(['item', 'isAscendingAuction'])
  const {t} = useLocale()
  const {goToPath} = usePrevRouteStore()
</script>

<template>
  <tr class="history-all-item">
    <!-- Date Bid -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'date-bid'">
      <div v-show="isLowerThanSM">{{ t('bidHistory.endDate') }}：</div>
      {{ format(item.end_datetime, 'yyyy/MM/dd') }}
    </td>

    <!-- Maker -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'maker'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.maker') }}：</div>
      {{ item.free_field?.maker }}
    </td>

    <!-- Product Name -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'itemname'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.productName') }}：</div>
      <a @click="conditionalNavigate(item.link, goToPath, $route)" class="cursor-pointer">
        {{ item.free_field?.product_name }}
      </a>
    </td>

    <!-- SIM -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'sim'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.sim') }}：</div>
      {{ item.free_field?.sim }}
    </td>

    <!-- Capacity -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'capacity'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.capacity') }}：</div>
      {{ item.free_field?.capacity }}
    </td>

    <!-- Color -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'color'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.color') }}：</div>
      {{ item.free_field?.color }}
    </td>

    <!-- Rank -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'grade'">
      <div v-show="isLowerThanSM">{{ t('productDetail.info.rank') }}：</div>
      {{ item.free_field?.rank }}
    </td>

    <!-- LowestBidPrice -->
    <td :class="isLowerThanSM ? 'sp-cell-flex' : 'price-min item-flex-center'">
      <div v-show="isLowerThanSM">{{ isAscendingAuction ? t('bidHistory.bidSuccessPrice') : t('bidHistory.bidSuccessUnitPrice') }}：</div>
      <div>
        <span class="unit">$</span>
        <span>
          {{ priceLocaleString(item.bid_success_price) }}
        </span>
      </div>
    </td>
  </tr>
</template>

<style lang="css" scoped>
  .sp-cell-flex {
    display: flex !important;
    justify-content: space-between;
  }
  .item-flex-center {
    display: flex;
    justify-content: center;
  }
</style>
