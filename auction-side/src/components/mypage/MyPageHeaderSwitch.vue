<script setup>
  import {PATH_NAME} from '@/defined/const'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'

  const route = useRoute()
  const {t: translate} = useLocale()

  const isActive = path => {
    return route.path === path ? 'active' : ''
  }
</script>
<template>
  <h2 class="page-ttl mypage">
    <p class="ttl">マイページ</p>
    <p class="sub">Mypage</p>
  </h2>

  <section id="mypage-head">
    <div class="container">
      <div class="nav-wrap">
        <div class="nav-content" :class="isActive(PATH_NAME.FAVORITES)">
          <RouterLink :to="PATH_NAME.FAVORITES">
            <span class="favorite"></span>
            <div class="label">{{ translate('favorite.title') }}</div>
          </RouterLink>
        </div>
        <div class="nav-content" :class="isActive(PATH_NAME.BIDS)">
          <RouterLink :to="PATH_NAME.BIDS">
            <span class="bidding"></span>
            <div class="label">{{ translate('auction.bidOngoing') }}</div>
          </RouterLink>
        </div>
        <div class="nav-content" :class="isActive(PATH_NAME.BID_HISTORY)">
          <RouterLink :to="PATH_NAME.BID_HISTORY">
            <span class="winning-history"></span>
            <div class="label">{{ translate('auction.bidHistory') }}</div>
          </RouterLink>
        </div>
        <div class="nav-content" :class="isActive(PATH_NAME.MYPAGE)">
          <RouterLink :to="PATH_NAME.MYPAGE">
            <span class="account"></span>
            <div class="label">{{ translate('user.editProfile') }}</div>
            <span class="upload"></span>
          </RouterLink>
        </div>
      </div>
    </div>
  </section>
</template>
