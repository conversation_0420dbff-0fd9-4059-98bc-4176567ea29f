<script setup>
  import {computed, defineAsyncComponent, onMounted, watch} from 'vue'
  import {onBeforeRouteLeave, useRoute} from 'vue-router'
  import {CLASSIFICATIONS, PATH_NAME} from '../../defined/const'

  import RowBidAuctionItem from '@/components/common/auction/RowBidAuctionItem.vue'
  import BidConfirmModal from '@/components/common/BidConfirmModal.vue'
  import BreadCrumb from '@/components/common/BreadCrumb.vue'
  import {useLocale} from 'vuetify'
  import ClassificationSwitch from '../../components/common/ClassificationSwitch.vue'
  import useSearchProducts from '../../composables/searchProducts'
  import useWebSocket from '../../composables/websocket'
  import {useAuthStore} from '../../stores/auth'
  import {useBidConfirmStore} from '../../stores/bidConfirm'
  import {useSearchResultStore} from '../../stores/search-results'
  import MyPageHeaderSwitch from './MyPageHeaderSwitch.vue'

  // const ProductList = defineAsyncComponent(() => import(/* WebpackChunkName: "ProductList" */ '../../components/search-list/ProductList.vue'))
  const FilterBox = defineAsyncComponent(() => import(/* webpackChunkName: "FilterBox" */ '../../components/search-list/parts/FilterBox.vue'))

  const {t, current} = useLocale()
  const route = useRoute()
  const searchResultStore = useSearchResultStore()
  const bid = useBidConfirmStore()
  const {search: searchOnLoad, resetParams, getConstants} = useSearchProducts()
  const auth = useAuthStore()
  const {connectWs} = useWebSocket(searchResultStore, route)

  const productList = computed(() => searchResultStore.productList.all)
  const isAscendingAuction = computed(() => searchResultStore.myPageSelectedClassification === CLASSIFICATIONS.ASCENDING)

  // Temporary flag to disable BidConfirmModal
  const ChangeMember = defineAsyncComponent(() => import('../../components/change-member/ChangeMember.vue'))

  const getData = async (classification = CLASSIFICATIONS.SEALED) => {
    searchResultStore.myPageSelectedClassification = classification
    const path = route.path
    if (path === PATH_NAME.FAVORITES) {
      await searchOnLoad({
        // favorite: true,
        // auction_classification: classification,
      })
    } else if (path === PATH_NAME.BIDS) {
      await searchOnLoad({
        // bidding: true,
        // unSoldOut: true,
        // auction_classification: classification,
      })
    } else if (path === PATH_NAME.BID_HISTORY) {
      await searchOnLoad({
        // successfulBid: true,
        // unSoldOut: true,
        // auction_classification: classification,
      })
    }
  }

  const getInitData = async () => {
    await Promise.all([getConstants(), getData()])
  }

  // 各画面取得
  const handleSearch = async () => {
    resetParams()
    await getData()
  }

  const refreshList = async classification => {
    const currentClassification = classification || searchResultStore.myPageSelectedClassification || CLASSIFICATIONS.SEALED
    await getData(currentClassification)
  }

  // パスが異なる場合のみ、再レンダリングする。
  watch(
    () => route.path,
    (newPath, oldPath) => {
      if (newPath !== oldPath) {
        handleSearch()
      }
    }
  )

  watch(
    () => current?.value,
    async () => {
      await getInitData()
    }
  )

  // 画面表示時に検索処理を実行
  onMounted(async () => {
    // ログインしている場合、WebSocket接続
    if (auth.token) {
      connectWs(auth.token)
    }

    resetParams()
    await getInitData()
  })

  onBeforeRouteLeave(() => {
    searchResultStore.viewMore = 1
  })
</script>
<template>
  <main id="main" class="mypage">
    <BreadCrumb :items="[{text: t(route.meta.label), to: route.path}]" />

    <!-- Header switch -->
    <MyPageHeaderSwitch />

    <section id="list">
      <div class="container">
        <!-- Show content for FAVORITES, BIDS, and BID_HISTORY routes -->
        <template v-if="route.path !== PATH_NAME.MYPAGE">
          <ClassificationSwitch @click:changeClassification="getData" />
          <div class="display-option">
            <div class="refine">
              <div class="count">
                <p><span>4</span>件のオークション</p>
              </div>
            </div>
          </div>

          <!-- 商品一覧 -->
          <RowBidAuctionItem v-for="item in productList" :key="item.exhibition_item_no" :item="item" @refresh="refreshList" />
        </template>

        <!-- Show ChangeMember component only for MYPAGE route -->
        <ChangeMember v-if="route.path === PATH_NAME.MYPAGE" />
      </div>
    </section>

    <!-- TODO: No longer use this?? -->
    <!-- <section id="list-auction">
      <div class="container list-auction-favorite">
        <template v-for="exh in exhibitionList" :key="exh.exhibition_no">
          <ProductList
            v-if="productList.some(x => x.exhibition_no === exh.exhibition_no)"
            :productList="productList.filter(x => x.exhibition_no === exh.exhibition_no)"
            :exhibitionInfo="exh"
            @refresh="refreshList"
          />
        </template>
        <FixBottomBulkBid v-if="!isAscendingAuction" />
      </div>
    </section> -->

    <BidConfirmModal v-model="bid.showBidConfirm" @refresh="refreshList" :isAscendingAuction="isAscendingAuction" />
  </main>
</template>
