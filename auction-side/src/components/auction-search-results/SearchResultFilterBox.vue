<script setup lang="ts">
  // import {defineAsyncComponent} from 'vue'
  // import {useRoute} from 'vue-router'
  // import {PATH_NAME} from '@/defined/const'
</script>

<template>
  <section id="list-head">
    <div class="container">
      <div class="search-panel-wrap">
        <div class="head">商品検索</div>
        <div class="contents">
          <div class="keyword">
            <div class="keyword__label">キーワード</div>
            <input type="text" data-id="shop-search-keyword" value="" class="search-keyword" placeholder="商品名・キーワードで探す" />
          </div>
          <div class="model">
            <div class="model__label">カテゴリー1</div>
            <div class="model__contents">
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">かばん・バッグ</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">財布</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">靴</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">サングラス</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">アクセサリー</label></div>
              <div class="label-item">
                <input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">ジャケット・ウェア</label>
              </div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">帽子</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">メンズライン</label></div>
            </div>
          </div>
          <div class="model">
            <div class="model__label">カテゴリー2</div>
            <div class="model__contents">
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">LOUIS VUITTON</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">CHANEL</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">HERMES</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">GUCCI</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">PRADA</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">BURBERRY</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">FENDI</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">CELINE</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">Christian Dior</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">ETRO</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">FENDI</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">GIVENCHY</label></div>
            </div>
          </div>
          <div class="model">
            <div class="model__label">カテゴリー3</div>
            <div class="model__contents">
              <div class="label-item">
                <input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">ハンドバッグ・2way</label>
              </div>
              <div class="label-item">
                <input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">ショルダーバッグ</label>
              </div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">トートバッグ</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">メンズバッグ</label></div>
              <div class="label-item"><input id="checkbox" class="checkbox-model" type="checkbox" /><label for="checkbox">その他バッグ</label></div>
            </div>
          </div>
          <div class="wrap-btn">
            <a class="clear">検索条件のクリア</a>
            <button class="btn search">検索する</button>
          </div>
        </div>
      </div>
      <div class="conditions">
        <div class="conditions__label">
          <p class="ttl">検索条件</p>
          <p class="elm"><span>LOUIS VUITTON</span><span>トートバッグ</span><span>ショルダーバッグ</span></p>
        </div>
        <div class="results__label">
          <p class="ttl">検索結果</p>
          <p class="elm">281<span>件</span></p>
        </div>
      </div>
    </div>
  </section>
</template>

<style lang="css" scoped>
  .search-keyword {
    background-color: #fff;
  }
</style>
