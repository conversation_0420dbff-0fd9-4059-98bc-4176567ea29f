<script setup>
  import {computed, onBeforeMount, watch} from 'vue'
  import {RouterLink} from 'vue-router'
  import {useLocale} from 'vuetify'
  import useNotice from '../../composables/useNotice'
  import {PATH_NAME} from '../../defined/const'

  defineProps({
    showMore: {
      type: Boolean,
      default: true,
    },
  })

  const {t, current} = useLocale()
  const {getNotices, countUpShowMore, notices, totalCount} = useNotice()

  const isShowLoadMore = computed(() => {
    return notices.length < totalCount.value
  })

  const loadMore = () => {
    countUpShowMore()
    getNotices({
      displayCodes: [1, 2],
    })
  }

  onBeforeMount(() => {
    getNotices({
      displayCodes: [1, 2],
    })
  })

  watch(
    () => current.value,
    () => {
      getNotices({
        displayCodes: [1, 2],
      })
    }
  )
</script>
<template>
  <section id="info" class="">
    <div class="container">
      <div id="infoNotice" class="info-box-notice">
        <h2>{{ t('notice.noticeLabel') }}</h2>
        <ul class="info-item">
          <li v-for="notice in notices" :key="notice.title">
            <RouterLink :to="`${PATH_NAME.NOTICE_LIST}/${notice.notice_no}`" class="headline">
              <p class="status"><span v-if="notice.is_new">NEW</span></p>
              <p class="notice-day">{{ notice.create_date }}</p>
              <p class="ttl">{{ notice.title }}</p>
            </RouterLink>
          </li>
        </ul>
        <div class="newslist-more" v-if="isShowLoadMore">
          <button class="btn" @click="loadMore">
            <span class="txt">{{ t('notice.more') }}<span class="arrow"></span></span>
          </button>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
  #info {
    padding: 20px 1rem !important;
  }
</style>
<style scoped>
  /* Normal state */
  .custom-link {
    color: black; /* Set the initial text color */
    text-decoration: none; /* Remove default underline */
  }

  /* Hover state */
  .custom-link:hover {
    color: blue; /* Change text color on hover */
    text-decoration: underline; /* Add underline on hover */
  }
</style>
