<script setup>
  import icnLogout from '@/assets/img/common/icn_nav_logout.svg'
  import icnMember from '@/assets/img/common/icn_nav_member.svg'
  import {PATH_NAME} from '@/defined/const'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth'
  import {computed} from 'vue'
  import {useRouter} from 'vue-router'

  const auth = useCognitoAuthStore()
  const router = useRouter()

  const buttonLabel = computed(() => {
    return auth.isAuthenticated ? 'ログアウト' : 'ログイン'
  })

  const buttonIcon = computed(() => {
    return auth.isAuthenticated ? icnLogout : icnMember
  })

  const handleAuthAction = () => {
    if (auth.isAuthenticated) {
      auth.logout()
      router.push(PATH_NAME.LOGIN)
    } else {
      router.push(PATH_NAME.LOGIN)
    }
  }
</script>

<template>
  <!-- Header -->
  <header>
    <!-- gNav PC/SP start -->
    <div class="wrap-header-elm">
      <div class="h-top">
        <p class="btnMenu only_sp"><span class="ham"></span></p>
        <h1 class="h-top-logo">
          <a class="logo" href="/"><img src="@/assets/img/common/logo_cecauction.png" alt="CEC AUCTION" /></a>
        </h1>
        <div class="h-top-menu only_sp">
          <ul>
            <li>
              <a class="btn-favorite" href=""><img src="@/assets/img/common/icn_header_favorite.svg" /></a>
            </li>
            <li>
              <a class="btn-member" href="./login.php"><img src="@/assets/img/common/icn_nav_member.svg" /></a>
            </li>
          </ul>
        </div>
      </div>
      <div class="nav-elm">
        <div class="search-elm only_pc">
          <div class="search-keyword">
            <input
              type="text"
              data-id="shop-search-keyword"
              value=""
              class="side-search-keyword search-keyword"
              placeholder="商品名・キーワードで探す"
            />
            <button>
              <img src="@/assets/img/common/icn_search_gray.svg" />
            </button>
          </div>
          <div class="info-menu">
            <li>
              <a href="#" class="nav-label">サイトについて</a>
              <div class="menu-list">
                <p class="arrow-box"></p>
                <ul>
                  <li class="#"><a href="./other/visitor/">初めての方へ</a></li>
                  <li class="#">
                    <a href="./other/guide/">ショッピングガイド</a>
                  </li>
                  <li class="#"><a href="./other/faq/">よくあるご質問</a></li>
                  <li class="#"><a href="./inquiry/">お問い合わせ</a></li>
                  <li class="#">
                    <a href="./other/member/">会員サービスについて</a>
                  </li>
                </ul>
              </div>
            </li>
          </div>
        </div>
        <ul class="nav-btn only_pc">
          <li class="nav-mypage favorite">
            <RouterLink :to="PATH_NAME.FAVORITES"> <img src="@/assets/img/common/icn_header_favorite.svg" /><span>お気に入り</span> </RouterLink>
          </li>
          <li class="nav-mypage bid">
            <RouterLink :to="PATH_NAME.BIDS"> <img src="@/assets/img/common/icn_bid.svg" class="bid" /><span>入札中</span> </RouterLink>
          </li>
          <li class="nav-mypage bidded">
            <RouterLink :to="PATH_NAME.BID_HISTORY">
              <img src="@/assets/img/common/icn_bidded.svg" class="bidded" /><span>落札履歴</span>
            </RouterLink>
          </li>
          <li class="nav-mypage login-out">
            <a @click="handleAuthAction" class="auth-button">
              <img :src="buttonIcon" /><span>{{ buttonLabel }}</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
    <!-- gNav PC/SP end -->
    <!-- gNav SP start -->
    <div class="gNav only_sp">
      <nav>
        <ul class="only_sp">
          <!--
            <li class="search">
              <input
                type="text"
                data-id="shop-search-keyword"
                value=""
                class="search-keyword"
                placeholder="商品名・キーワードで探す"
              />
              <button><img src="@/assets/img/common/icn_search_dgray.svg" /></button>
            </li>
            -->
          <li class="account">
            <button class="btn mypage">マイページ</button>
            <button class="btn logout auth-button" @click="handleAuthAction">
              {{ buttonLabel }}
            </button>
          </li>
          <li class="nav-black">
            <p>商品カテゴリー</p>
            <ul>
              <li><a href="/list_watch/">キャンペーン早割商品</a></li>
              <li><a href="/list_bag/">クーポン適用商品</a></li>
              <li><a href="/list_bag/">オフィス・事務用品</a></li>
              <li><a href="/list_bag/">コンピュータ</a></li>
              <li><a href="/list_bag/">AV機器・家電</a></li>
              <li><a href="/list_bag/">新規会員限定割引</a></li>
              <li><a href="/list_bag/">AV機器・家電</a></li>
              <li><a href="/list_bag/">店舗什器・備品</a></li>
              <li><a href="/list_bag/">建築資材・廃材</a></li>
            </ul>
          </li>
          <li class="nav-black">
            <p>会員について</p>
            <ul>
              <li>
                <a @click="handleAuthAction" class="auth-button">{{ buttonLabel }}</a>
              </li>
              <li><a href="A">新規会員登録</a></li>
              <li><a href="A">マイページ</a></li>
            </ul>
          </li>
          <li class="nav-black">
            <p>初めての方へ</p>
            <ul>
              <li><a href="A">WEBオークションの流れ</a></li>
              <li><a href="A">出品したい方</a></li>
            </ul>
          </li>
          <li class="nav-black">
            <p>ご案内</p>
            <ul>
              <li><a href="/login.html">よくあるご質問</a></li>
              <li><a href="/entryinfo.html">お問い合わせ</a></li>
              <li><a href="/mypage.html" target="_blank">会社案内</a></li>
              <li><a href="./terms.html">利用規約</a></li>
            </ul>
          </li>
        </ul>
        <div class="line-logo">
          <div class="cont-wrap">
            <div class="pct">
              <a href="/"><img src="@/assets/img/common/logo_cecauction.svg" /></a>
            </div>
            <div class="sns">
              <ul>
                <li>
                  <a href="A" class=""><img src="@/assets/img/common/icn_sns_facebook.svg" class="facebook" /></a>
                </li>
                <li>
                  <a href="A" class=""><img src="@/assets/img/common/icn_sns_x.svg" class="x" /></a>
                </li>
                <li>
                  <a href="A" class=""><img src="@/assets/img/common/icn_sns_instagram.svg" class="instagram" /></a>
                </li>
                <li>
                  <a href="A" class=""><img src="@/assets/img/common/icn_sns_youtube.svg" class="youtube" /></a>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="line-copyright">
          <div class="cont-wrap">
            <ul>
              <li><a href="./">特定商取引法に基づく表記</a></li>
              <li><a href="./">プライバシーポリシー</a></li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
    <!-- gNav SP end -->
  </header>
</template>

<style lang="css" scoped>
  .nav-elm {
    align-items: center;
  }
  .info-menu {
    width: 120px !important;
  }

  .auth-button {
    cursor: pointer;
    transition: opacity 0.2s ease;
  }

  .auth-button:hover {
    opacity: 0.8;
  }

  button.auth-button {
    background: none;
    border: none;
    font: inherit;
    color: inherit;
    text-decoration: none;
  }

  a.auth-button {
    text-decoration: none;
  }
</style>
