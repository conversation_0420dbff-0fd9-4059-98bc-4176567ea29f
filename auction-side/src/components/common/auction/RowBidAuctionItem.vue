<script setup>
  import useBid from '@/composables/bid'
  import {formatDateString, priceLocaleString} from '@/composables/common'
  import useFavorite from '@/composables/favorite'
  import {usePrevRouteStore} from '@/stores/prev-route'
  import {conditionalNavigate} from '@/utils'
  import {defineEmits, defineProps} from 'vue'
  import {useLocale} from 'vuetify'

  import topItemImage from '@/assets/img/item/top_item01.png'
  const props = defineProps(['item'])
  const emit = defineEmits(['refresh'])

  const {t} = useLocale()
  const {goToPath} = usePrevRouteStore()
  const {toggleFavorite} = useFavorite()
  const {bidPrice, bidQuantity, bidHandle, validateBidPrice, validateBidQuantity} = useBid()

  // Helper function to determine auction status class
  const getAuctionStatusClass = item => {
    if (!item || !item.bid_status) return ''

    const now = new Date()
    const endDate = new Date(item.bid_status.end_datetime || item.end_datetime)
    const startDate = new Date(item.bid_status.start_datetime || item.start_datetime)

    // Check if sold out
    if (item.sold_out || item.bid_status.sold_out) {
      return 'soldout'
    }

    // Check if auction has ended
    if (now > endDate) {
      return 'closed'
    }

    // Check if auction hasn't started yet
    if (now < startDate) {
      return 'preauc'
    }

    // Check if auction is being extended
    if (item.bid_status.extending) {
      return 'extended'
    }

    // Default to new auction (empty class)
    return ''
  }

  // Helper function to format remaining time
  const getRemainingTime = endDatetime => {
    if (!endDatetime) return ''

    const now = new Date()
    const endTime = new Date(endDatetime)
    const diff = endTime - now

    if (diff <= 0) {
      return '終了'
    }

    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) {
      return `残り${hours}時間${minutes}分`
    } else {
      return `残り${minutes}分`
    }
  }

  // Helper function to format end datetime display
  const getEndDateTimeDisplay = endDatetime => {
    if (!endDatetime) return ''

    const {datePart, timePart} = formatDateString(endDatetime)
    return `（${datePart} ${timePart} 終了予定）`
  }

  // Helper function to handle bid price increment
  const incrementBidPrice = (currentPrice, increment) => {
    const current = parseInt(currentPrice?.replace(/[^0-9]/g, '') || '0')
    bidPrice.value = (current + increment).toLocaleString()
  }

  // Handle bid button click with proper parameter mapping
  const handleBidClick = item => {
    // Check if bidPrice has a value
    if (!bidPrice.value) {
      console.log('🔵 No bid price entered!')
      return
    }

    // Set the bid quantity to 1 if not set (for sealed auctions)
    if (!bidQuantity.value) {
      bidQuantity.value = '1'
    }

    const bidParams = {
      exhibitionNo: item.exhibition_no,
      exhibitionItemNo: item.exhibition_item_no,
      exhibitionName: item.exhibition_name,
      lowestBidPrice: item.bid_status?.lowest_bid_price,
      lowestBidQuantity: item.bid_status?.lowest_bid_quantity || 1,
      pitchWidth: item.bid_status?.pitch_width || 1,
      freeField: item.free_field,
      newBidPrice: bidPrice.value,
      newBidQuantity: bidQuantity.value || '1',
      currentBidPrice: item.bid_status?.current_price,
      enteredBidPrice: item.bid_status?.bid_price,
      enteredBidQuantity: item.bid_status?.bid_quantity,
      maxQuantity: item.bid_status?.quantity,
      isAscendingAuction: false, // StandardAuctionItem is for sealed auctions
      hasUserBid: item.bid_status?.has_user_bid || false,
    }
    bidHandle(bidParams)
  }
</script>

<template>
  <div class="item-list row-bid">
    <ul>
      <li :class="getAuctionStatusClass(item)">
        <figure>
          <img :src="item?.free_field?.image_url || topItemImage" />
          <div class="tab-f" v-if="item?.status === 2"><span class="title-a">New</span></div>
        </figure>

        <div class="item-p-desc">
          <p class="item-name">
            <a @click="conditionalNavigate(item?.link, goToPath, $route)" class="cursor-pointer">
              <span class="name-title">{{ item?.free_field?.product_name || '' }}</span>
              <span class="tab-item" v-if="item?.free_field?.shipping_free">送料無料</span>
            </a>
          </p>

          <div class="summary-wrap">
            <div class="desc-p-top">
              <div class="price-box">
                <p class="price">
                  <span class="price-c">現在</span>
                  <span class="price-v">{{ priceLocaleString(item?.bid_status?.current_price) || '0' }}</span>
                  <span class="price-u">円</span>
                </p>
                <p class="price" v-if="item?.free_field?.instant_price">
                  <span class="price-c">即決価格</span>
                  <span class="price-v bl">{{ priceLocaleString(item?.free_field?.instant_price) }}</span>
                  <span class="price-u bl">円</span>
                </p>
                <p class="price" v-if="item?.bid_status?.lowest_bid_price">
                  <span class="price-c">最低入札価格</span>
                  <span class="price-v bl sm">{{ priceLocaleString(item?.bid_status?.lowest_bid_price) }}</span>
                  <span class="price-u bl sm">円</span>
                </p>
              </div>
              <ul class="tab-wrap-status">
                <li class="top" v-if="item?.bid_status?.is_top_member">あなたがTOP</li>
                <li class="min-bid" v-if="item?.bid_status?.minimum_bid_exceeded">最低落札超え</li>
              </ul>
            </div>

            <ul class="tab-wrap" v-if="item?.free_field">
              <li class="tab-main" v-if="item?.free_field?.condition">{{ item?.free_field?.condition }}</li>
              <li class="tab-sub" v-if="item?.free_field?.maker">{{ item?.free_field?.maker }}</li>
              <li class="tab-wari" v-if="item?.free_field?.color">{{ item?.free_field?.color }}</li>
            </ul>

            <ul class="pre-bid">
              <li class="view">
                <p>{{ item?.attention_info?.view_count || 0 }}</p>
              </li>
              <li class="favo">
                <p>{{ item?.attention_info?.favorited_count || 0 }}</p>
              </li>
              <li class="bid-v">
                <p>{{ item?.attention_info?.bid_count || 0 }}</p>
              </li>
              <li class="end-v">
                <p>
                  <span class="date red">{{ getRemainingTime(item?.bid_status?.end_datetime || item?.end_datetime) }}</span>
                  <span class="end">{{ getEndDateTimeDisplay(item?.bid_status?.end_datetime || item?.end_datetime) }}</span>
                </p>
              </li>
            </ul>
            <div class="btn-foreground-wrap">
              <button class="btn refresh" @click="emit('refresh')"></button>
              <button
                class="btn favorite"
                :class="{active: item?.attention_info?.is_favorited}"
                @click="toggleFavorite(item?.exhibition_item_no)"
              ></button>
            </div>
          </div>
        </div>

        <div class="place-bid">
          <div class="price">
            <span class="ttl">入札価格</span>
            <input
              type="text"
              data-id="price-bid"
              v-model="bidPrice"
              class="price-bid"
              :placeholder="priceLocaleString(item?.bid_status?.current_price) || '1,000'"
            />円
          </div>
          <ul class="bidding-unit">
            <li>
              <button class="bid-unit" @click="incrementBidPrice(bidPrice, 10000)"><span class="icn_add"></span>10,000円</button>
            </li>
            <li>
              <button class="bid-unit" @click="incrementBidPrice(bidPrice, 50000)"><span class="icn_add"></span>50,000円</button>
            </li>
            <li>
              <button class="bid-unit" @click="incrementBidPrice(bidPrice, 100000)"><span class="icn_add"></span>100,000円</button>
            </li>
          </ul>
          <div class="button-bid">
            <button class="btn" @click="handleBidClick(item)" :disabled="!bidPrice">
              <img class="pct" src="@/assets/img/common/icn_bid_w.svg" />
              <span class="bid">入札する</span>
            </button>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<style lang="css" scoped>
  .price-bid {
    background: white;
  }
  .button-bid .btn {
    cursor: pointer;
  }
</style>
