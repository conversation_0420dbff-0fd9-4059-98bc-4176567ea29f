import {computed, ref, watch} from 'vue'

/**
 * View mode types for product list display
 * - 'panel': Grid-like panel view with larger product cards
 * - 'row': Row-based view with detailed bidding interface
 */
export type ViewMode = 'panel' | 'row'

const STORAGE_KEY = 'product-list-view-mode'

const getInitialViewMode = (): ViewMode => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored === 'panel' || stored === 'row') {
      return stored
    }
  }
  return 'panel'
}

const viewMode = ref<ViewMode>(getInitialViewMode())

export function useViewMode() {
  /**
   * Toggle view mode between 'panel' and 'row'
   * Automatically persists the selection to localStorage
   */
  const toggleView = (mode: ViewMode) => {
    viewMode.value = mode
  }

  /**
   * Watch for view mode changes and persist to localStorage
   */
  watch(
    viewMode,
    newMode => {
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEY, newMode)
      }
    },
    {immediate: false}
  )
  const isPanelMode = computed(() => viewMode.value === 'panel')

  const isRowMode = computed(() => viewMode.value === 'row')

  return {
    viewMode,
    toggleView,
    isPanelMode,
    isRowMode,
  }
}
