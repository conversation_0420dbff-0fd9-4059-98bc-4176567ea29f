import type {FormattedAuctionItem} from '@/composables/_type'

export type ProductListHandlers = {
  /** Handle favorite toggle action */
  onFavoriteToggle?: (exhibitionItemNo: string, currentFavorited: boolean) => Promise<void>
  /** Handle bid placement */
  onBid?: (item: FormattedAuctionItem, bidPrice: string, bidQuantity: string) => Promise<void>
  /** Handle refresh action */
  onRefresh?: () => Promise<void>
  /** Handle item click navigation */
  onItemClick?: (item: FormattedAuctionItem) => void
}
