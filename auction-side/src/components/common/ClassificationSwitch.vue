<script setup>
  import {computed} from 'vue'
  import {useLocale} from 'vuetify'
  import {CLASSIFICATIONS} from '../../defined/const'
  import {useSearchResultStore} from '../../stores/search-results'
  import {isMobile} from '../../utils'

  const searchResultStore = useSearchResultStore()

  const {t} = useLocale()

  const isAscendingActive = computed(() => searchResultStore.myPageSelectedClassification === CLASSIFICATIONS.ASCENDING)

  const emit = defineEmits(['click:changeClassification'])
</script>

<template>
  <div class="bid-tab-wrap">
    <a class="tab-cont" :class="{active: isAscendingActive}" @click="emit('click:changeClassification', CLASSIFICATIONS.ASCENDING)">
      <div class="label">
        <span v-if="!isMobile">{{ t('CLASSIFICATION_ASCENDING') }}</span>
        <span v-else>{{ t('ASCENDING') }}</span>
      </div>
    </a>
    <a class="tab-cont" :class="{active: !isAscendingActive}" @click="emit('click:changeClassification', CLASSIFICATIONS.SEALED)">
      <div class="label">
        <span v-if="!isMobile">{{ t('CLASSIFICATION_SEALED') }}</span>
        <span v-else>{{ t('SEALED') }}</span>
      </div>
    </a>
  </div>
</template>

<style lang="css" scoped>
  #main #list .container .bid-tab-wrap {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: end;
    gap: 3px;
    width: 100%;
    margin: 100px 0 40px;
    border-bottom: 1px solid #333;
  }
  @media screen and (max-width: 767px) {
    #main #list .container .bid-tab-wrap {
      margin: 10vw 0 4vw;
    }
  }
  #main #list .container .bid-tab-wrap .tab-cont,
  #main #list .container .bid-tab-wrap .tab {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 380px;
    max-width: 50%;
    margin: 0;
    padding: 0;
    border: 1px solid;
  }
  @media screen and (max-width: 767px) {
    #main #list .container .bid-tab-wrap .tab-cont,
    #main #list .container .bid-tab-wrap .tab {
      height: 12vw;
    }
  }
  #main #list .container .bid-tab-wrap .tab-cont {
    border-color: #c9c9c9;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    border-bottom: none;
    height: 56px;
    cursor: pointer;
  }
  @media screen and (max-width: 767px) {
    #main #list .container .bid-tab-wrap .tab-cont {
      max-width: 50%;
      margin: 0;
    }
  }
  #main #list .container .bid-tab-wrap .tab-cont .label {
    display: inline-block;
    padding: 2px 0 0;
    color: #c9c9c9;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.2;
  }
  @media screen and (max-width: 767px) {
    #main #list .container .bid-tab-wrap .tab-cont .label {
      padding: 1vw 0 0;
      font-size: 3.5vw;
    }
  }
  #main #list .container .bid-tab-wrap .tab-cont:after {
    display: none;
  }
  #main #list .container .bid-tab-wrap .tab-cont.active {
    border-color: #333;
    height: 62px;
    cursor: default;
  }
  #main #list .container .bid-tab-wrap .tab-cont.active .label {
    color: #333;
    padding: 5px 0 0;
  }
  #main #list .container .bid-tab-wrap .tab-cont.active:after {
    position: absolute;
    content: '';
    bottom: -2px;
    width: 100%;
    height: 3px;
    background-color: #fff;
    z-index: 1;
    display: block;
  }
  @media screen and (max-width: 767px) {
    #main #list .container .bid-tab-wrap .tab-cont {
      height: 12vw;
    }
    #main #list .container .bid-tab-wrap .tab-cont.active {
      height: 14vw;
    }
    #main #list .container .bid-tab-wrap .tab-cont.active:after {
      bottom: -1vw;
      height: 2vw;
    }
  }
  /* Hover effects */
  #main #list .container .bid-tab-wrap .tab-cont:hover {
    opacity: 1;
    border-color: #333;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
  }
  #main #list .container .bid-tab-wrap .tab-cont:hover .label {
    color: #333;
  }
  #main #list .container .bid-tab-wrap .tab-cont.active:hover {
    cursor: default;
  }
</style>
