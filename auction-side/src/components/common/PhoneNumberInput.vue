<script setup>
  import {computed, defineEmits, defineProps, onMounted, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'

  const emit = defineEmits(['update:value'])
  const props = defineProps({
    country: {
      type: String,
      default: '',
    },
    isConfirm: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    maxlength: {
      type: Object,
      default: () => ({}),
    },
  })

  const countryCode = ref('') // +81
  const tel = ref('')
  const {t} = useLocale()

  const isJapan = computed(() => props.country === 'JP')

  const reset = newValue => {
    countryCode.value = isJapan.value ? '' : newValue.countryCode || ''
    tel.value = newValue.tel || ''
  }

  onMounted(() => {
    reset(props.value)
  })

  watch(
    () => props.value,
    newValue => {
      reset(newValue)
    }
  )

  const convertToHalfWidth = input => input.replace(/[０-９]/g, char => String.fromCharCode(char.charCodeAt(0) - 0xfee0))

  const onChange = () => {
    tel.value = convertToHalfWidth(tel.value)
    countryCode.value = convertToHalfWidth(countryCode.value)
    emit('update:value', {
      countryCode: countryCode.value,
      tel: tel.value,
    })
  }
</script>

<template>
  <div class="phone-wrap">
    <template v-if="isConfirm">
      <span>{{ `${countryCode}${tel}` }}</span>
    </template>
    <template v-else>
      <div v-if="!isJapan" class="code-wrap">
        <p class="label-country-code" v-html="t('register.form.telCountryCode')"></p>
        <!-- telCountryCode -->
        <input
          type="tel"
          class="ime-dis country-code bg-white"
          placeholder=""
          required
          :maxlength="maxlength.telCountryCode"
          v-model="countryCode"
          @change="onChange"
        />
      </div>
      <!-- tel -->
      <input
        type="tel"
        :class="{
          'ime-dis': true,
          'bg-white': true,
          'iptW-M': isJapan,
          'phone-number': !isJapan,
        }"
        :maxlength="maxlength.tel"
        placeholder=""
        required
        v-model="tel"
        @change="onChange"
      />
    </template>
  </div>
</template>
