<script setup>
  import {computed, defineEmits, defineProps, onMounted, ref, watch} from 'vue'
  import {useLocale} from 'vuetify'
  import {isMobile} from '../../utils'

  const emit = defineEmits(['update:value'])
  const props = defineProps({
    country: {
      type: String,
      default: '',
    },
    isConfirm: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    maxlength: {
      type: Object,
      default: () => ({}),
    },
  })

  const {t} = useLocale()

  const firstName = ref('') // 名前
  const lastName = ref('') // 苗字

  const isJapan = computed(() => props.country === 'JP')

  const reset = newValue => {
    firstName.value = newValue.firstName || ''
    lastName.value = isJapan.value ? newValue.lastName || '' : ''
  }

  onMounted(() => {
    reset(props.value)
  })

  watch(
    () => props.value,
    newValue => {
      reset(newValue)
    }
  )

  const onChange = () => {
    emit('update:value', {
      firstName: firstName.value,
      lastName: lastName.value,
    })
  }
</script>
<template>
  <div>
    <!-- 確認モード -->
    <template v-if="isConfirm">
      <span v-if="isJapan" class="font-weight-bold mr-1">{{ t('register.firstLastName.lastName') }}</span>
      <span v-if="isJapan" class="mx-4">{{ lastName }}</span>
      <span v-if="isJapan" class="font-weight-bold mx-1">{{ t('register.firstLastName.firstName') }}</span>
      <span class="mx-4">{{ firstName }}</span>
    </template>

    <!-- 入力モード -->
    <template v-else>
      <!-- Mobile Layout -->
      <div v-if="isMobile" class="sp-first-last-name">
        <div v-if="isJapan" class="sp-field">
          <label v-if="isJapan">{{ t('register.firstLastName.lastName') }}</label>
          <input
            v-if="isJapan"
            type="text"
            class="iptW-S bg-white"
            placeholder=""
            required
            v-model="lastName"
            @change="onChange"
            :maxlength="maxlength.lastName"
          />
        </div>
        <div v-if="isJapan" class="divident"></div>
        <div class="sp-field">
          <label v-if="isJapan">{{ t('register.firstLastName.firstName') }}</label>
          <input
            type="text"
            :class="{
              'bg-white': true,
              'iptW-S': isJapan,
              'iptW-M': !isJapan,
            }"
            placeholder=""
            required
            v-model="firstName"
            @change="onChange"
            :maxlength="maxlength.firstName"
          />
        </div>
      </div>

      <!-- Non-Mobile Layout -->
      <div v-else>
        <span v-if="isJapan" class="mr-1">{{ t('register.firstLastName.lastName') }}</span>
        <input
          v-if="isJapan"
          type="text"
          class="iptW-S bg-white"
          placeholder=""
          required
          v-model="lastName"
          @change="onChange"
          :maxlength="maxlength.lastName"
        />
        <span v-if="isJapan" class="mx-1">{{ t('register.firstLastName.firstName') }}</span>
        <input
          type="text"
          :class="{
            'bg-white': true,
            'iptW-S': isJapan,
            'iptW-M': !isJapan,
          }"
          placeholder=""
          required
          v-model="firstName"
          @change="onChange"
          :maxlength="maxlength.firstName"
        />
      </div>
    </template>
  </div>
</template>

<style scoped>
  .sp-first-last-name {
    display: flex;
  }
  .sp-field {
    margin-bottom: 1rem;
    width: 100%;
  }

  .sp-field label {
    display: block;
    margin-bottom: 0.5rem;
  }
  .sp-field input {
    width: 100%;
    max-width: 100% !important;
  }
  .sp-field .iptW-S {
    width: 100% !important;
  }
  .divident {
    width: 10px;
  }
</style>
