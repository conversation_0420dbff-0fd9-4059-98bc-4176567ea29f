<script setup>
  import termsJa from '@/assets/pdf/bid/利用規約_20250326.pdf'
  import termsEn from '@/assets/pdf/bid/terms_of_service_20250324.pdf'
  import ModalDialog from '../common/ModalDialog.vue'
  import {computed, ref, defineModel, watch} from 'vue'
  import {useBidConfirmStore} from '../../stores/bidConfirm'
  import {priceLocaleString} from '../../composables/common'
  import useApi from '../../composables/useApi'
  import {useMessageDialogStore} from '../../stores/messag-dialog'
  import {CLASSIFICATIONS, PATH_NAME} from '../../defined/const'
  import useSearchProducts from '../../composables/searchProducts'
  import useGetItemDetails from '../../composables/getItemDetails'
  import {useRoute} from 'vue-router'
  import {eventBus} from '../../utils'
  import {useCognitoAuthStore} from '@/stores/cognitoAuth'

  const emit = defineEmits(['refresh'])
  const props = defineProps(['isAscendingAuction'])

  const open = defineModel()

  const current = ref('ja')
  const route = useRoute()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const bid = useBidConfirmStore()
  const messageDialog = useMessageDialogStore()
  const {search: searchList} = useSearchProducts()
  const {search: searchDetail} = useGetItemDetails()
  const hasBid = ref(false)
  // const agree = ref(false)

  const auth = useCognitoAuthStore()

  const termsSrc = computed(() => {
    return current.value === 'ja' ? termsJa : termsEn
  })
  const showBidResult = computed(() => bid.showBidResult)
  const exhibitionList = computed(() => bid.data || [])
  const allExhTotalPrice = computed(() => {
    return priceLocaleString(exhibitionList.value.reduce((acc, exh) => acc + (exh?.bidTotalPrice || 0), 0))
  })

  const sendBid = async (inputParams = [{exhibitionItemNo: null, bidPrice: null, bidQuantity: null}]) => {
    const params = {
      bidItems: inputParams.map(({exhibitionItemNo, bidPrice, bidQuantity}) => ({
        exhibitionItemNo,
        bidPrice,
        bidQuantity,
      })),
    }
    await apiExecute('private/bid-items', params)
      .then(response => {
        bid.data.map(exh => {
          exh.bidList.map(item => {
            item.errorMessage = null
            const tmp = response?.bidList?.find(x => String(item.exhibitionItemNo) === String(x.exhibition_item_no))
            item.errorMessage = tmp?.errorMessage
          })
          exh.bidTotalPrice = exh.bidList.reduce((acc, item) => {
            if (item.errorMessage) {
              return acc
            }
            return acc + (item.bidTotalPrice || 0)
          }, 0)
        })
      })
      .catch(error => {
        const errMsg = parseHtmlResponseError(error)
        messageDialog.setShowMessage(errMsg, {isErr: true})
      })
  }

  const handleBid = async () => {
    if (!bid.agree) {
      return
    }
    if (auth.isAuthenticated) {
      const params = exhibitionList.value
        .map(exh => exh.bidList)
        .flat()
        .map(item => ({
          exhibitionItemNo: item.exhibitionItemNo,
          bidQuantity: item.bidQuantity,
          bidPrice: item.bidPrice,
        }))
      await sendBid(params)
      bid.setShowBidResult(true)
      hasBid.value = true
      eventBus.emit('onBidSuccess', params[0].exhibitionItemNo, params[0].bidPrice)
      // open.value = false
      // messageDialog.setShowMessage(t('productDetail.bidModal.bidSuccessFulMessage'), {
      //   name            : 'bid-success',
      //   showOkButton    : true,
      //   showCloseButton : false
      // })
    } else {
      open.value = false
      messageDialog.setShowMessage('ログインが必要です。', {
        showOkButton: true,
        showCloseButton: false,
      })
    }
  }

  const closeBidConfirmModal = () => {
    open.value = false
    if (hasBid.value) {
      const classification = props.isAscendingAuction ? CLASSIFICATIONS.ASCENDING : CLASSIFICATIONS.SEALED
      // Only emit 'refresh' if a bid was made and send isAscendingAuction as props to prevent switch to sealed tab 「ascending」 bid
      emit('refresh', classification)
      hasBid.value = false
    }
  }

  watch(
    () => messageDialog.clickedOk,
    async value => {
      if (value) {
        switch (messageDialog.dialogName) {
          case 'bid-success':
            if (route.path === PATH_NAME.FAVORITES) {
              searchList({favorite: true})
            } else if (route.path === PATH_NAME.BIDS) {
              searchList({bidding: true, unSoldOut: true})
            } else if (route.path === PATH_NAME.DETAIL) {
              await searchDetail(route.params.manageNo ?? '')
            }
            break
          default:
            break
        }
      }
      messageDialog.clickedOk = false
    }
  )
</script>
<template>
  <ModalDialog v-model="open" width="900px" @refresh-on-close="closeBidConfirmModal">
    <div>
      <!--ModalBid Start-->
      <div class="matching-dir-wrap" id="scrollable">
        <div v-for="exh in exhibitionList" :key="exh.exhibitionNo" class="auction-line-wrap">
          <p class="auction-ttl">{{ exh.exhibitionName }}</p>
          <ul class="bid-list">
            <li v-for="item in exh.bidList" :key="item.exhibitionItemNo">
              <p class="bid-item-name">
                <span class="label">{{ item.freeField?.maker }}</span>
                <!-- <span>:</span> -->
                <span>{{ item.freeField?.product_name }}</span>
              </p>
              <p v-if="!isAscendingAuction">
                <span class="label">入札数量</span><span class="cont">{{ priceLocaleString(item.bidQuantity) }}</span>
              </p>
              <p>
                <span class="label">{{ isAscendingAuction ? '入札価格' : '入札単価' }}</span
                ><span class="cont">${{ priceLocaleString(item.bidPrice) }}</span>
              </p>
              <p v-if="!isAscendingAuction">
                <span class="label">入札合計価格</span>
                <span class="cont">${{ priceLocaleString(item.bidTotalPrice) }}</span>
              </p>
              <div v-if="showBidResult" class="criterion">
                <dl
                  :class="{
                    success: !item.errorMessage,
                    failure: item.errorMessage,
                  }"
                >
                  <dt v-if="item.errorMessage">入札失敗</dt>
                  <dt v-else>入札成功</dt>
                  <dd>{{ item.errorMessage }}</dd>
                </dl>
              </div>
            </li>
          </ul>
          <div class="auction-total">
            <p class="label">出品合計価格</p>
            <p class="price">${{ priceLocaleString(exh.bidTotalPrice) }}</p>
          </div>
        </div>
        <div class="total-bid-amount">
          <p>全出品合計価格</p>
          <p class="price">${{ allExhTotalPrice }}</p>
        </div>
      </div>
      <template v-if="showBidResult">
        <div class="button-bid mt-5">
          <button class="btn bg-grey-lighten-1" @click="closeBidConfirmModal">閉じる</button>
        </div>
      </template>
      <template v-else>
        <p class="note-bid">入札後のキャンセルはできません。</p>
        <!-- Terms agreement checkbox -->
        <div class="terms-agreement">
          <label class="checkbox-container">
            <input type="checkbox" v-model="bid.agree" class="checkbox-input" />
            <span class="checkbox-parts">
              <a :href="termsSrc" target="_blank" rel="noopener noreferrer"> 利用規約 </a>
              に同意する
            </span>
          </label>
        </div>
        <div class="button-bid">
          <button class="btn" @click="handleBid" :disabled="!bid.agree">入札する</button>
        </div>
      </template>
      <!--ModalBid Start-->
    </div>
  </ModalDialog>
</template>

<style scoped lang="scss">
  .btn {
    width: 100px;
    height: 50px;
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 4px;
    cursor: pointer;

    &:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
  }

  .button-bid {
    margin: 20px;
  }

  .terms-agreement {
    margin: 15px 20px;
    text-align: center;

    .checkbox-container {
      display: inline-flex;
      align-items: center;
      cursor: pointer;

      .checkbox-input {
        margin-right: 8px;
      }

      .checkbox-parts {
        font-size: 14px;

        a {
          color: #007bff;
          text-decoration: underline;

          &:hover {
            text-decoration: none;
          }
        }
      }
    }
  }
</style>
