<script setup>
  import {computed, defineProps, ref} from 'vue'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'
  import useApi from '../../composables/useApi'
  import ModalDialog from '../common/ModalDialog.vue'

  const props = defineProps({
    token: {
      type: String,
    },
  })
  const open = defineModel()

  const route = useRoute()
  const router = useRoute()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const {t: translate} = useLocale()

  const currentPassword = ref('')
  const password = ref('')
  const passwordConfirm = ref('')
  const errorMsg = ref({
    currentPassword: null,
    password: null,
    passwordConfirm: null,
    passwordConfirmCpr: null,
  })

  const checkInput = () => {
    errorMsg.value.currentPassword = null
    errorMsg.value.password = null
    errorMsg.value.passwordConfirm = null
    errorMsg.value.passwordConfirmCpr = null

    if (currentPassword.value === '') {
      errorMsg.value.currentPassword = '現在のパスワードを確認してください。'
    }
    if (password.value === '') {
      errorMsg.value.password = '新しいパスワードを確認してください。'
    }
    if (passwordConfirm.value === '') {
      errorMsg.value.passwordConfirm = '新しいパスワード(確認)を確認してください。'
    }
    if (password.value !== passwordConfirm.value) {
      errorMsg.value.passwordConfirmCpr = '新しいパスワードと新しいパスワード(確認)が一致しません。'
    }

    if (errorMsg.value.currentPassword || errorMsg.value.password || errorMsg.value.passwordConfirm || errorMsg.value.passwordConfirmCpr) {
      return false
    }
    return true
  }

  const isLoginDisabled = computed(() => {
    return !(currentPassword.value && password.value && passwordConfirm.value && password.value === passwordConfirm.value)
  })

  const sendRequest = async () => {
    console.log('sendRequest')
    await apiExecute(
      'private/change-member-password',
      {
        currentPassword: currentPassword.value,
        password: password.value,
        passwordConfirm: passwordConfirm.value,
      },
      false,
      {
        authorization: props.token,
      }
    )
      .then(res => {
        console.log('sendRequest res', res)
        open.value = false
      })
      .catch(error => {
        const errs = parseHtmlResponseError(error)
        errorMsg.value = {...errorMsg.value, ...errs}
      })
  }

  const handleClick = async () => {
    if (checkInput()) {
      await sendRequest()
    }
  }
</script>
<template>
  <ModalDialog v-model="open">
    <section>
      <h1 class="mb1">{{ translate('login.reSettingPasswordAtFirstTime') }}</h1>
      <div class="container">
        <section>
          <form>
            <table class="tbl-login">
              <tbody>
                <tr>
                  <th>{{ translate('login.currentPassword') }}<em class="req">※</em></th>
                  <td>
                    <input
                      type="password"
                      v-bind:class="{
                        'ime-dis': true,
                        err: errorMsg.currentPassword,
                      }"
                      required
                      v-model="currentPassword"
                    />
                    <p v-show="errorMsg.currentPassword" class="err-txt">
                      {{ errorMsg.currentPassword }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <th>{{ translate('login.newPassword') }}<em class="req">※</em></th>
                  <td>
                    <input
                      type="password"
                      v-bind:class="{
                        'ime-dis': true,
                        err: errorMsg.password,
                      }"
                      required
                      v-model="password"
                    />
                    <p v-show="errorMsg.password" class="err-txt">
                      {{ errorMsg.password }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <th class="text-break">{{ translate('login.newPasswordConfirm') }}<em class="req">※</em></th>
                  <td>
                    <input
                      type="password"
                      v-bind:class="{
                        'ime-dis': true,
                        err: errorMsg.passwordConfirm,
                      }"
                      required
                      v-model="passwordConfirm"
                    />
                    <p v-show="errorMsg.passwordConfirm || errorMsg.passwordConfirmCpr" class="err-txt">
                      {{ errorMsg.passwordConfirm || errorMsg.passwordConfirmCpr }}
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="btn-form">
              <input type="button" :value="`${translate('common.send')}`" @click="handleClick" :disabled="isLoginDisabled" />
            </div>
          </form>
        </section>
      </div>
    </section>
  </ModalDialog>
</template>
