<script setup>
  defineOptions({
    name: 'PasswordReminder',
  })

  import {useReminder} from '@/stores/reminder'
  import {computed, ref} from 'vue'
  import {useLocale} from 'vuetify'
  import useApi from '../../composables/useApi'
  import {API_PATH /* REGIST_FORM */, PATTERN} from '../../defined/const'

  const {apiExecute, parseHtmlResponseError} = useApi()

  const email = ref('')
  const confirmEmail = ref('')
  const reminderStore = useReminder()
  const errorMsg = ref('')
  const {t: translate} = useLocale()

  const checkInput = computed(() => {
    const check = {
      email: {pass: false, errMsg: translate('login.reminderEmailCheckError')},
      confirmEmail: {
        pass: false,
        errMsg: translate('login.reminderConfirmEmailCheckError'),
      },
    }
    if (PATTERN.EMAIL.test(email.value)) {
      check.email.pass = true
      check.email.errMsg = ''
    }
    if (PATTERN.EMAIL.test(confirmEmail.value) && email.value === confirmEmail.value) {
      check.confirmEmail.pass = true
      check.confirmEmail.errMsg = ''
    }
    return check
  })

  /**
   *
   */
  const sendRequest = async () => {
    const requestData = {user_id: null, email: email.value}
    await apiExecute(API_PATH.REISSUE_PASSWORD, requestData)
      .then(data => {
        reminderStore.completedFlag = true
      })
      .catch(error => {
        errorMsg.value = parseHtmlResponseError(error)?.errorMessage
        console.log({errorMsg})
      })
  }
</script>
<template>
  <main id="main" class="reminder">
    <div id="pNav" class="bgLGray">
      <ul>
        <li><a href="./">TOP</a></li>
        <li>{{ translate('login.reminderForgetPassword') }}</li>
      </ul>
    </div>
    <section id="login">
      <h1 class="mb0">{{ translate('login.reminderForgetPassword') }}</h1>
      <div class="container">
        <div class="remind-msg">
          <p class="txtCoCor remind-txt-att">
            <span>
              {{ translate('login.reminderMessage1') }}
            </span>
            <span>
              {{ translate('login.reminderMessage2') }}
            </span>
          </p>
        </div>
        <section id="login-form">
          <form>
            <table class="tbl-login">
              <tbody>
                <tr>
                  <th>
                    {{ translate('login.email') }}
                  </th>
                  <td>
                    <input type="text" :class="email === '' || checkInput.email.pass ? 'ime-dis' : errClass" required v-model="email" />
                    <p v-show="email !== '' && !checkInput.email.pass" class="err-txt">
                      {{ checkInput.email.errMsg }}
                    </p>
                  </td>
                </tr>
                <tr>
                  <th class="work-break">
                    {{ translate('login.emailConfirm') }}
                  </th>
                  <td>
                    <input
                      type="text"
                      :class="confirmEmail === '' || checkInput.confirmEmail.pass ? 'ime-dis' : errClass"
                      required
                      v-model="confirmEmail"
                    />
                    <p v-show="confirmEmail !== '' && !checkInput.confirmEmail.pass" class="err-txt">
                      {{ checkInput.confirmEmail.errMsg }}
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>

            <div class="remind-msg" v-if="errorMsg">
              <p class="txtCoCor remind-txt-att">
                <span> {{ errorMsg }}</span>
              </p>
            </div>

            <div class="btn-form">
              <input
                type="button"
                :value="translate('login.sendButton')"
                @click="sendRequest"
                :disabled="!(checkInput.email.pass && checkInput.confirmEmail.pass)"
              />
            </div>
          </form>
        </section>
      </div>
    </section>
  </main>
</template>

<style scoped>
  .work-break {
    word-break: break-word;
  }
</style>
