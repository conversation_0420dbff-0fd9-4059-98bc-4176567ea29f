<script setup>
  import {defineAsyncComponent, onBeforeMount, reactive, ref, watch} from 'vue'
  import {useRouter} from 'vue-router'
  import {useLocale} from 'vuetify'
  import {convertFullWidthToHalfWidth, scrollToTop} from '../../composables/common'
  import useApi from '../../composables/useApi'
  import {PATH_NAME} from '../../defined/const'
  import {useMessageDialogStore} from '../../stores/messag-dialog'
  import {useRegisterStore} from '../../stores/useRegister'

  const EntryForm = defineAsyncComponent(() => import(/* webpackChunkName: "EntryForm" */ './EntryForm.vue'))
  const ConfirmData = defineAsyncComponent(() => import(/* webpackChunkName: "ConfirmData" */ './ConfirmData.vue'))

  const router = useRouter()
  const {t, current} = useLocale()
  const dialog = useMessageDialogStore()
  const {resetParams} = useRegisterStore()
  const {apiExecute, parseHtmlResponseError} = useApi()

  const step = ref(1)
  const showError = ref(false)
  const constants = ref([])

  const errorMsg = reactive({
    country: null,
    ceoName: null,
    ceoNameKana: null,
    ceoBirthday: null,
    companyName: null,
    companyNameKana: null,
    companyAddress: null,
    establishmentDate: null,
    companyHp: null,
    businessContent: null,
    invoiceNo: null,
    tel: null,
    antiquePermitNo: null,
    antiquePermitDate: null,
    antiquePermitCommission: null,
    memberLastName: null,
    memberName: null,
    whatsApp: null,
    wechat: null,
    memo: null,
    email: null,
    emailConfirm: null,
    password: null,
    passwordConfirm: null,
    emailDuplicated: null,
  })

  const nextStep = () => {
    step.value += 1
  }
  const prevStep = () => {
    step.value -= 1
  }

  const fullWidthToHalfWidthConversion = params => {
    const registerData = {...params}
    Object.keys(registerData).map(key => {
      if (key === 'antiquePermitNo' || key === 'password' || key === 'passwordConfirm' || key === 'tel') {
        registerData[key] = convertFullWidthToHalfWidth(registerData[key])
      }
    })
    return registerData
  }

  const sendConfirmRequest = async params => {
    const registerData = fullWidthToHalfWidthConversion(params)
    const reqParams = {registerData, validateFlag: true}
    try {
      Object.keys(errorMsg).map(key => {
        errorMsg[key] = null
      })
      await apiExecute('request-member', reqParams)
      scrollToTop()
      if (!Object.values(errorMsg).some(val => val)) {
        nextStep()
      }
    } catch (error) {
      const err = parseHtmlResponseError(error)
      scrollToTop()
      Object.keys(err).map(x => {
        if (err[x]) {
          errorMsg[x] = err[x]
        }
      })
    }
  }

  const sendRequest = async params => {
    const registerData = fullWidthToHalfWidthConversion(params)
    const reqParams = {registerData}
    try {
      await apiExecute('request-member', reqParams)
      scrollToTop()
      dialog.setShowMessage(t('register.form.registerFinish'), {
        showOkButton: true,
        showCloseButton: false,
      })
      showError.value = false
      watch(
        () => dialog.showMessageDialog,
        newVal => {
          if (!newVal && !showError.value) {
            router.push(PATH_NAME.TOP)
          }
        }
      )
    } catch (error) {
      console.log(error)
      scrollToTop()
      dialog.setShowMessage(t('common.error'), {
        showOkButton: true,
        showCloseButton: false,
      })
    }
  }

  const getMemberRegisterConstants = async () => {
    await apiExecute('get-member-regist-constants')
      .then(data => {
        constants.value = data
      })
      .catch(error => {
        const errors = parseHtmlResponseError(router, error)
        console.log(errors)
      })
  }

  onBeforeMount(() => {
    resetParams()
    getMemberRegisterConstants()
  })

  watch(
    () => current.value,
    () => {
      getMemberRegisterConstants()
    }
  )

  const handleErrorMsg = ({field, message}) => {
    errorMsg[field] = message
  }
  const emailLangList = [
    {title: 'EN', value: 'en'},
    {title: 'JA', value: 'ja'},
  ]
</script>
<template>
  <div id="pNav">
    <ul>
      <li><a href="../">TOP</a></li>
      <li>{{ t('register.title') }}</li>
    </ul>
  </div>
  <section id="entry">
    <h1 class="mb0">{{ t('register.title') }}</h1>
    <div class="container">
      <EntryForm
        v-if="step === 1"
        :constants="constants"
        :errorMsg="errorMsg"
        @confirm-inputs="sendConfirmRequest"
        @update:errorMsg="handleErrorMsg"
        :emailLangOptions="emailLangList"
      />
      <ConfirmData
        v-else-if="step === 2"
        :constants="constants"
        :errorMsg="errorMsg"
        @back="prevStep"
        @regist-member="sendRequest"
        :emailLangOptions="emailLangList"
      />
    </div>
  </section>
</template>
