import {useMessageDialogStore} from '@/stores/messag-dialog'
import {onBeforeMount, reactive, ref, watch} from 'vue'
import {useRouter} from 'vue-router'
import {useLocale} from 'vuetify'
import {convertFullWidthToHalfWidth, scrollToTop} from '../../../composables/common'
import useApi from '../../../composables/useApi'
import {PATH_NAME} from '../../../defined/const'
import {useRegisterStore} from '../../../stores/useRegister'

// Types defined in composable
export type ErrorMsg = {
  country: string | null
  ceoName: string | null
  ceoNameKana: string | null
  ceoBirthday: string | null
  companyName: string | null
  companyNameKana: string | null
  companyAddress: string | null
  establishmentDate: string | null
  companyHp: string | null
  businessContent: string | null
  invoiceNo: string | null
  tel: string | null
  antiquePermitNo: string | null
  antiquePermitDate: string | null
  antiquePermitCommission: string | null
  memberLastName: string | null
  memberName: string | null
  whatsApp: string | null
  weChat: string | null
  memo: string | null
  email: string | null
  emailConfirm: string | null
  password: string | null
  passwordConfirm: string | null
  emailDuplicated: string | null
}

export type EmailLangOption = {
  title: string
  value: string
}

export type RegisterData = {
  [key: string]: any
}

export type HandleErrorMsgParams = {
  field: keyof ErrorMsg
  message: string
}

export function useRegister() {
  const router = useRouter()
  const {t, current} = useLocale()
  const dialog = useMessageDialogStore()
  const {resetParams} = useRegisterStore()
  const {apiExecute, parseHtmlResponseError} = useApi()

  // Utility functions
  const fullWidthToHalfWidthConversion = (params: RegisterData): RegisterData => {
    const registerData = {...params}
    const fieldsToConvert = ['antiquePermitNo', 'password', 'passwordConfirm', 'tel']

    Object.keys(registerData).forEach(key => {
      if (fieldsToConvert.includes(key) && registerData[key]) {
        registerData[key] = convertFullWidthToHalfWidth(registerData[key])
      }
    })

    return registerData
  }

  const emailLangList: EmailLangOption[] = [
    {title: 'EN', value: 'en'},
    {title: 'JA', value: 'ja'},
  ]

  // State
  const step = ref(1)
  const showError = ref(false)
  const constants = ref<any[]>([])

  const errorMsg = reactive<ErrorMsg>({
    country: null,
    ceoName: null,
    ceoNameKana: null,
    ceoBirthday: null,
    companyName: null,
    companyNameKana: null,
    companyAddress: null,
    establishmentDate: null,
    companyHp: null,
    businessContent: null,
    invoiceNo: null,
    tel: null,
    antiquePermitNo: null,
    antiquePermitDate: null,
    antiquePermitCommission: null,
    memberLastName: null,
    memberName: null,
    whatsApp: null,
    weChat: null,
    memo: null,
    email: null,
    emailConfirm: null,
    password: null,
    passwordConfirm: null,
    emailDuplicated: null,
  })

  // Actions
  const nextStep = () => {
    step.value += 1
  }

  const prevStep = () => {
    step.value -= 1
  }

  const sendConfirmRequest = async (params: RegisterData) => {
    const registerData = fullWidthToHalfWidthConversion(params)
    const reqParams = {registerData, validateFlag: true}

    try {
      // Clear previous errors
      Object.keys(errorMsg).forEach(key => {
        errorMsg[key as keyof ErrorMsg] = null
      })

      await apiExecute('request-member', reqParams)
      scrollToTop()

      if (!Object.values(errorMsg).some(val => val)) {
        nextStep()
      }
    } catch (error: any) {
      const err = parseHtmlResponseError(error)
      scrollToTop()

      Object.keys(err).forEach(key => {
        if (err[key] && key in errorMsg) {
          errorMsg[key as keyof ErrorMsg] = err[key]
        }
      })
    }
  }

  const sendRequest = async (params: RegisterData) => {
    const registerData = fullWidthToHalfWidthConversion(params)
    const reqParams = {registerData}

    try {
      await apiExecute('request-member', reqParams)
      scrollToTop()

      dialog.setShowMessage(t('register.form.registerFinish'), {
        showOkButton: true,
        showCloseButton: false,
      })

      showError.value = false

      watch(
        () => dialog.showMessageDialog,
        newVal => {
          if (!newVal && !showError.value) {
            router.push(PATH_NAME.TOP)
          }
        }
      )
    } catch (error: any) {
      console.error('Error registering member:', error)
      scrollToTop()
      dialog.setShowMessage(t('common.error'), {
        showOkButton: true,
        showCloseButton: false,
      })
    }
  }

  const getMemberRegisterConstants = async () => {
    try {
      const data = await apiExecute('get-member-regist-constants')
      constants.value = data
    } catch (error: any) {
      const errors = parseHtmlResponseError(error)
      console.error('Error getting register constants:', errors)
    }
  }

  const handleErrorMsg = ({field, message}: HandleErrorMsgParams) => {
    errorMsg[field] = message
  }

  // Watchers
  watch(
    () => current.value,
    () => {
      getMemberRegisterConstants()
    }
  )

  // Lifecycle
  onBeforeMount(() => {
    resetParams()
    getMemberRegisterConstants()
  })

  return {
    // State
    step,
    constants,
    errorMsg,
    emailLangList,

    // Actions
    nextStep,
    prevStep,
    sendConfirmRequest,
    sendRequest,
    handleErrorMsg,

    // Utils
    t,
  }
}
