@charset "UTF-8";
/***********************************************************************
 * *
 * *------------------------------------------------------------------------
 * *ログインページ
 * *********************************************************************** */
#main #login-form {
  margin: 40px auto 100px;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #login-form {
    margin: 0 auto;
    padding: 7vw 0 20vw;
  }
}
#main #login-form .id-pass-err {
  width: 560px;
  max-width: 100%;
  text-align: center;
  margin: 1.5rem auto;
}
@media screen and (max-width: 767px) {
  #main #login-form .id-pass-err {
    margin: 4vw 0 10vw;
  }
}
#main #login-form .id-pass-err .err-txt {
  color: #ff0000;
  font-weight: 500;
}
@media screen and (max-width: 767px) {
  #main #login-form .id-pass-err .err-txt {
    font-size: 3.5vw;
  }
}
#main #login-form table.tbl-login {
  width: 560px;
  max-width: 100%;
  margin: 0 auto;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login {
    width: 100%;
    margin: 0;
  }
}
#main #login-form table.tbl-login tr {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  margin: 0 0 1.5rem;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login tr {
    margin: 0 0 4vw;
  }
}
#main #login-form table.tbl-login tr:last-child {
  margin: 0;
}
#main #login-form table.tbl-login th {
  display: block;
  position: relative;
  width: 100%;
  padding: 0.2rem 0.5rem;
  font-size: 0.9rem;
  font-weight: 600;
  vertical-align: bottom;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th {
    display: block;
    width: 100%;
    padding: 0 1vw;
    font-size: 3.5vw;
    font-weight: 500;
  }
}
#main #login-form table.tbl-login th em.req {
  display: inline-block;
  position: absolute;
  top: 7px;
  right: 0.5rem;
  font-size: 12px;
  font-weight: 700;
  color: #f00;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th em.req {
    top: 0.5vw;
    right: 1vw;
    padding: 0;
    font-size: 3vw;
  }
}
#main #login-form table.tbl-login td {
  padding: 0;
  position: relative;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login td {
    display: block;
    width: 100%;
    padding: 0.5vw 0 0;
  }
}
#main #login-form table.tbl-login td .password-wrapper {
  position: relative;
  width: 100%;
  margin: 0 0 1em;
}
#main #login-form table.tbl-login td .password-wrapper .toggle-visibility {
  position: absolute;
  top: 50%;
  right: 20px;
  width: 20px;
  height: 20px;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  outline: none;
}
#main #login-form table.tbl-login td .password-wrapper .toggle-visibility.eye-show {
  background-image: url('../img/common/icon_eye.png');
  background-size: 20px auto;
  background-repeat: no-repeat;
  background-position: center;
}
#main #login-form table.tbl-login td .password-wrapper .toggle-visibility.eye-hide {
  background-image: url('../img/common/icon_eye_inactive.png');
  background-size: 20px auto;
  background-repeat: no-repeat;
  background-position: center;
}
#main #login-form table.tbl-login td .password-wrapper .toggle-visibility:hover {
  opacity: 0.8;
}
#main #login-form table.tbl-login td input {
  width: 100%;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login td input {
    width: 100%;
    height: 14vw;
  }
}
#main #login-form table.tbl-login td .err-txt {
  font-size: 0.9rem;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login td .err-txt {
    position: static;
    -webkit-transform: none;
    transform: none;
    max-width: 100%;
    margin-top: 5px;
    font-size: 3.8vw;
  }
}
#main #login-form table.tbl-login th span.note {
  display: inline-block;
  width: 16px;
  height: 16px;
  text-align: center;
  line-height: 1;
  padding-top: 3px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th span.note {
    width: 4.4vw;
    height: 4.4vw;
  }
}
#main #login-form table.tbl-login th span.note:hover {
  cursor: pointer;
}
#main #login-form table.tbl-login th .note-modal {
  display: none;
  z-index: 1;
  position: absolute;
  top: 85px;
  left: -60px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .note-modal {
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
  }
}
#main #login-form table.tbl-login th .note-modal .note-txt {
  text-align: center;
  background-color: #fff;
  -webkit-box-shadow: 0px 5px 15px 1px rgba(0, 0, 0, 0.08);
  box-shadow: 0px 5px 15px 1px rgba(0, 0, 0, 0.08);
  width: 500px;
  padding: 30px 15px;
  border-radius: 8px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .note-modal .note-txt {
    position: fixed;
    width: calc(100vw - 50px);
    top: 50%;
    right: 50%;
    -webkit-transform: translate(50%, -50%);
    transform: translate(50%, -50%);
  }
}
#main #login-form table.tbl-login th .note-modal .note-txt:before {
  content: '';
  display: block;
  border-right: 10px solid transparent;
  border-bottom: 14px solid #fff;
  border-left: 10px solid transparent;
  position: absolute;
  top: -14px;
  left: 150px;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .note-modal .note-txt:before {
    display: none;
  }
}
#main #login-form table.tbl-login th .note-modal .note-txt p {
  display: inline-block;
  font-size: 16px;
  color: #427fae;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.8;
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .note-modal .note-txt p {
    font-size: 3.8vw;
    line-height: 1.6;
  }
}
@media screen and (max-width: 767px) {
  #main #login-form table.tbl-login th .modal__bg {
    background: rgba(0, 0, 0, 0.7);
    height: 100vh;
    position: absolute;
    width: 100vw;
  }
}
#main #login-form .check-idpass {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin: 1.5rem 0 0;
}
@media screen and (max-width: 767px) {
  #main #login-form .check-idpass {
    margin: 7vw 0 0;
    padding-left: 0;
    text-align: center;
  }
}
#main #login-form .forget-pass {
  margin: 1.5rem 0 0;
  text-align: center;
}
#main #login-form .forget-pass a {
  display: inline-block;
  font-weight: 700;
  color: #427fae;
  font-size: 14px;
  padding: 0;
}
@media screen and (max-width: 767px) {
  #main #login-form .forget-pass a {
    font-size: 3.5vw;
  }
}
#main #login-form .forget-pass a:hover {
  text-decoration: underline;
  opacity: 1;
}
#main #login-form .rule {
  max-width: 550px;
  margin: 70px auto 0;
}
#main #login-form .rule p.tit-rule {
  font-weight: 500;
  color: #000;
  font-size: 0.9rem;
  padding: 0 0.5rem 0.2rem;
}
@media screen and (max-width: 767px) {
  #main #login-form .rule p.tit-rule {
    padding: 0 2vw 0.5vw;
  }
}
#main #login-form .rule embed {
  border: 1px solid #ccc;
}
#main #login-form .rule .rule-check {
  margin: 1.8rem 0 0;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #login-form .rule .rule-check {
    margin: 6vw 0 0;
  }
}
#main #login-form .btn-form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
@media screen and (max-width: 767px) {
  #main #login-form .btn-form {
    width: 100%;
  }
}
#main #login-form .btn-form input[type='button'] {
  height: 70px;
  border-radius: 40px;
}
@media screen and (max-width: 767px) {
  #main #login-form .btn-form input[type='button'] {
    width: 100%;
    height: 16vw;
    max-width: 100%;
    margin: 0;
    padding: 0;
    font-size: 4vw;
    border-radius: 20vw;
  }
}
#main #login-form .rule .rule-check p.err-txt {
  margin-top: 5px;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #login-form .rule .rule-check p.err-txt {
    margin: 2vw 0 0;
  }
}
#main #login-form .request {
  margin-top: 40px;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main #login-form .request {
    margin: 10vw 0 0;
  }
}
#main #login-form .request a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 380px;
  max-width: 100%;
  height: 70px;
  margin: 0 auto;
  padding: 0;
  color: #427fae;
  font-weight: 700;
  font-size: 1rem;
  background-color: #fff;
  border: 1px solid #427fae;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main #login-form .request a {
    width: 100%;
    height: 16vw;
    margin: 0;
    font-size: 4vw;
    border-radius: 20vw;
  }
}
#main #login-form .request a:hover {
  color: #fff;
  background-color: #427fae;
}
#main #login-form .request p {
  text-align: center;
  font-weight: 500;
  color: #333;
  font-size: 12px;
  margin: 10px 0 0;
}
@media screen and (max-width: 767px) {
  #main #login-form .request p {
    margin: 3vw 0 0;
    font-size: 3.5vw;
  }
}
#main.reminder [class^='remind-msg'] {
  width: 560px;
  max-width: 100%;
  margin: 0 auto;
  padding: 1.5rem 1.5rem;
  color: #e98181;
  background-color: #fff4f4;
  border: 1px solid #e98181;
  border-radius: 4px;
}
@media screen and (max-width: 767px) {
  #main.reminder [class^='remind-msg'] {
    width: 100%;
    margin: 0 auto;
    padding: 5vw 5vw;
    font-size: 3.8vw;
  }
}
#main.reminder [class^='remind-msg'] p {
  text-align: center;
  font-weight: 500;
  font-size: 0.9rem;
  text-align: left;
}
@media screen and (max-width: 767px) {
  #main.reminder [class^='remind-msg'] p {
    font-size: 3.8vw;
  }
}
#main.reminder [class^='remind-msg'] p.remind-txt-att span {
  display: inline-block;
  font-weight: 500;
}
#main.reminder [class^='remind-msg'] p.remind-txt-att a {
  color: #e98181;
  font-weight: 500;
  text-decoration: underline;
}
#main.reminder [class^='remind-msg'] p.remind-txt-att a:hover {
  text-decoration: none;
}
#main.reminder .container {
  padding: 1rem 1rem 80px;
}
@media screen and (max-width: 767px) {
  #main.reminder .container {
    padding: 4vw 4vw 7vw;
  }
}
#main.reminder .container .remind-msg-comp {
  margin: 0 auto 80px;
  text-align: center;
}
@media screen and (max-width: 767px) {
  #main.reminder .container .remind-msg-comp {
    margin: 0 auto 14vw;
  }
}
#main.reminder .container .remind-msg-comp p {
  text-align: center;
}
#main.reminder .container .remind-comp-btn {
  margin: 40px 0;
}
@media screen and (max-width: 767px) {
  #main.reminder .container .remind-comp-btn {
    margin: 10vw auto;
  }
}
#main.reminder .container .remind-comp-btn a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 280px;
  max-width: 100%;
  height: 54px;
  margin: 0 auto;
  padding: 0;
  color: #427fae;
  font-weight: 700;
  font-size: 1rem;
  background-color: #fff;
  border: 1px solid #427fae;
  border-radius: 50px;
}
@media screen and (max-width: 767px) {
  #main.reminder .container .remind-comp-btn a {
    width: 100%;
    height: 15vw;
    margin: 0;
    font-size: 4.2vw;
    border-radius: 20vw;
  }
}
#main.reminder .container .remind-comp-btn a:hover {
  color: #fff;
  background-color: #427fae;
}
/*# sourceMappingURL=login.css.map */
