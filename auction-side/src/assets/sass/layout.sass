@charset "utf-8"
@import "./mixin"

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *wrap
 ***********************************************************************/

.container
  width: 1280px
  max-width: 100%
  margin: 0 auto
  padding: 1rem
  @include sp
    width: 100%
    max-width: 100%
    padding: 4vw

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *header
 ***********************************************************************/

header
  border-bottom: 1px solid $line-p-gray
  background-color: #fff
  position: relative
  @include sp
    display: flex
    align-items: center
    justify-content: space-between
    width: 100%
    height: 14vw

  .wrap-header-elm
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    @include sp
      width: 100%


    .l-header-info-links
      display: flex
      flex-direction: row
      align-items: center
      width: 100%
      height: 34px
      padding: 0
      background-color: $line-p-gray
      @include sp
        display: none

      .l-header-info-item
        display: flex
        flex-direction: row
        justify-content: flex-end
        align-items: center
        width: calc(1280px + 2.2rem)
        max-width: 100%
        margin: 0 auto
        padding: 0 1.1rem
        @include md
          padding: 0 2rem

        li
          display: flex
          flex-direction: row
          align-items: center

          &.language-swich

            .lang-wrap
              position: relative
              margin: 0
              width: auto

              &:after
                content: ""
                display: block
                width: 4px
                height: 4px
                border-top: 2px solid #9e9e9e
                border-left: 2px solid #9e9e9e
                transform: rotate(-135deg)
                position: absolute
                right: 4px
                top: calc(50% - 3px)

              select
                display: flex
                align-items: center
                width: 100%
                height: 20px
                padding: 0 16px 2px 22px
                font-size: .8em
                font-weight: bold
                text-decoration: underline
                align-items: center
                background: url("../img/common/icn_global.svg") no-repeat 0 50%
                background-size: 16px 16px
                border: none
                appearance: none
                cursor: pointer

                option
                  position: relative
                  display: flex
                  align-items: center
                  justify-content: flex-end
                  padding: 0

    .main-nav-wrap
      display: flex
      flex-direction: row
      justify-content: space-between
      align-items: center
      width: calc(1280px + 2rem)
      max-width: 100%
      height: 76px
      margin: 0 auto
      padding: 0 1rem
      @include md
        height: auto
        padding: .5rem 1.5rem
      @include sp
        width: 100%
        max-width: 100%
        height: 100%
        margin: 0
        padding: 0

      .h-top
        display: flex
        justify-content: space-between
        align-items: center
        flex: 0 1 200px
        height: 65px
        padding: 0
        @include md
          height: auto
        @include sp
          position: relative
          display: flex
          flex-direction: row
          justify-content: space-between
          width: 100%
          min-height: 35px
          padding: 0
          height: 100%
          flex: auto

        .h-top-logo
          display: flex
          margin: 0
          align-items: center
          @include sp
            position: absolute
            left: 50%
            transform: translateX(-50%)
            margin: 0
            padding: 0
            height: 50px

          a.logo
            display: flex
            align-items: center
            width: 200px
            max-width: 38vw
            transition: none
            @include sp
              width: 30vw
              max-width: 200px

            img
              width: 100%
              height: auto

          p
            color: #fff
            font-size: 16px
            position: relative
            line-height: 1
            margin-left: 22px
            padding-left: 20px
            @include sp
              font-size: 2.5vw
              margin-left: 11px
              padding-left: 10px
              &::before
                width: 1px
                height: 15px

            &::before
              content: ""
              display: block
              width: 2px
              height: 28px
              background-color: #fff
              position: absolute8
              top: 50%
              left: 0
              transform: translateY(-50%)

        .h-top-menu
          position: relative
          display: flex
          align-items: center
          justify-content: flex-end
          width: 100px
          margin: 0 3.8vw 0 0

          .lang-wrap
            display: flex
            align-items: center
            position: relative
            margin: 0
            width: auto
            height: auto
            min-height: 5vw

            select
              display: flex
              align-items: center
              width: 100%
              height: 6vw
              padding: 0 0 0 6vw
              font-size: 3.8vw
              font-weight: bold
              text-decoration: underline
              align-items: center
              background: url("../img/common/icn_global.svg") no-repeat 0 calc(50% + .1vw)
              background-size: 4.8vw 4.8vw
              border: none
              appearance: none
              cursor: pointer

              option
                position: relative
                display: flex
                align-items: center
                justify-content: flex-end
                padding: 0


          ul
            display: flex
            flex-direction: row
            @include sp
              gap: 3vw

            li
              display: flex
              align-items: center
              justify-content: center
              width: 38px
              height: 38px
              border: .5px solid #fff
              @include sp
                width: auto
                height: 100%
                margin: 0


              a
                display: flex
                align-items: center
                justify-content: center
                width: 100%
                height: 100%
                color: #fff
                font-weight: 700
                font-size: 1rem
                background-repeat: no-repeat
                background-position: left center
                @include sp
                  font-size: 3.5vw

                &.btn-favorite
                  img
                    width: 24px
                    height: auto
                    @include sp
                      width: 5vw

                &.btn-member
                  img
                    width: 24px
                    height: auto
                    @include sp
                      width: 4.5vw

          a
            &.btn-bid
              background-size: 18px auto
              padding-left: 23px

            &.btn-fav
              background-size: 15px auto
              padding-left: 20px

            &.btn-logout
              background-size: 15px auto
              padding-left: 18px

            &.btn-page img
              width: 16px

            &.btn-lang
              background-size: 15px auto
              padding-left: 20px


      .nav-elm
        display: flex
        flex-direction: row
        @include md
          flex-direction: column

        .search-elm
          display: flex
          align-items: center
          height: auto
          margin: 0 1.5rem
          @include md
            justify-content: flex-end
            margin: 0 .5rem
            padding: .5rem 0

          .search-category
            display: flex
            align-items: center
            position: relative
            width: 110px
            height: 100%
            margin: 0 1.2rem 0 0
            color: $main-gray

            li
              display: flex
              align-items: center
              position: relative
              width: 100%
              height: 50px
              padding: 0
              list-style-type: none
              @include md
                height: 30px

              a.nav-label
                padding: 0
                display: flex
                align-items: center
                justify-content: flex-start
                width: 100%
                height: 100%
                color: $main-text
                font-size: .8rem
                font-weight: 600

                &:after
                  content: ""
                  position: absolute
                  right: 2px
                  top: 50%
                  width: 4px
                  height: 4px
                  border-right: 2px solid #bcbcbc
                  border-bottom: 2px solid #bcbcbc
                  transform: translateY(-50%) rotate(45deg)

                &:hover
                  color: $main-color
                  opacity: 1
                  &:after
                    border-color: $main-color

            &:hover .menu-list
              display: block

            .menu-list
              display: none/*design確認用*/
              position: absolute
              top: 50px
              left: -180px
              width: 800px
              margin: 0 auto
              padding: 0
              z-index: 20
              @include md
                top: 30px
                left: -180px
                width: 650px

              .arrow-box
                width: 100%
                height: 16px
                @include md

              .panel-wrap
                width: 100%
                padding: 20px 10px
                background-color: #f5f5f5
                border-radius: 10px
                box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35)

                .category-box
                  display: flex
                  flex-direction: row
                  padding: .5rem 1rem

                  .category
                    &-all
                      width: 100%
                      padding: 2px 5px
                      display: flex
                      flex-direction: row
                      flex-wrap: wrap
                      gap: 10px
                      width: 100%

                      p
                        font-size: .8rem
                        font-weight: 500

                        a
                          display: inline-block
                          padding: 1px 16px
                          color: $main-text
                          font-size: .8rem
                          font-weight: 500
                          transition: all 0.3s ease
                          white-space: nowrap
                          cursor: pointer

                          &:hover
                            opacity: 1
                            background-color: #e8e8e8
                            border-radius: 50px

                    &-top
                      width: 150px
                      padding: 2px 5px
                      border-right: 1px dotted #ccc

                      p
                        font-size: .8rem
                        font-weight: 500

                        a
                          display: inline-block
                          padding: 1px 16px
                          color: $main-text
                          font-size: .8rem
                          font-weight: 500
                          transition: all 0.3s ease
                          white-space: nowrap
                          cursor: pointer

                          &:hover
                            opacity: 1
                            background-color: #e8e8e8
                            border-radius: 50px


                    &-secondary
                      flex: 1

                      ul
                        display: flex
                        flex-direction: row
                        flex-wrap: wrap
                        width: 100%
                        padding-left: 10px

                        &:before
                          content: ""
                          position: absolute
                          top: 0
                          left: 220px
                          width: 0
                          height: 0
                          border-left: 10px solid transparent
                          border-right: 10px solid transparent
                          border-bottom: 16px solid #f5f5f5
                          @include md
                            left: 230px

                        li
                          position: relative
                          width: auto
                          height: auto
                          padding: 1px 2px

                          a
                            display: inline-block
                            padding: 1px 16px
                            color: $main-text
                            font-size: .8rem
                            font-weight: 500
                            transition: all 0.3s ease
                            white-space: nowrap

                            &:hover
                              opacity: 1
                              background-color: #e8e8e8
                              border-radius: 50px


          .info-menu
            display: flex
            align-items: center
            position: relative
            width: 108px
            height: 100%
            margin: 0 0 0 1.2rem
            color: $main-gray

            li
              display: flex
              align-items: center
              position: relative
              width: 100%
              height: 50px
              padding: 0
              list-style-type: none
              @include md
                height: 30px

              a.nav-label
                padding: 0
                display: flex
                align-items: center
                justify-content: flex-start
                width: 100%
                height: 100%
                color: $main-text
                font-size: .8rem
                font-weight: 600

                &:after
                  content: ""
                  position: absolute
                  right: 4px
                  top: calc(50% - 0px)
                  width: 4px
                  height: 4px
                  border-right: 2px solid #bcbcbc
                  border-bottom: 2px solid #bcbcbc
                  transform: translateY(-50%) rotate(45deg)

                &:hover
                  color: $main-color
                  opacity: 1
                  &:after
                    border-color: $main-color

            &:hover .menu-list
              display: block

            .menu-list
              display: none/*design確認用*/
              position: absolute
              top: 50px
              z-index: 20
              @include md
                top: 26px
                left: -100px

              .arrow-box
                width: 100%
                height: 16px

              ul
                width: 220px
                padding: 10px 0
                background-color: #f5f5f5
                border-radius: 10px
                box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35)

                &:before
                  content: ""
                  position: absolute
                  top: 0
                  left: 30px
                  width: 0
                  height: 0
                  border-left: 10px solid transparent
                  border-right: 10px solid transparent
                  border-bottom: 16px solid #f5f5f5
                  @include md
                    left: 134px
                  @include sp
                    left: 80px

                li
                  position: relative
                  width: 220px
                  height: 40px

                  a
                    display: flex
                    align-items: center
                    justify-content: flex-start
                    width: 100%
                    height: 100%
                    padding: 20px 20px
                    color: $main-gray
                    font-size: .8rem
                    font-weight: 500
                    transition: all 0.3s ease

                    &:hover
                      opacity: 1
                      background-color: #e8e8e8

          .search-keyword
            display: flex
            align-items: center
            width: 260px
            height: 44px
            border: 1px solid $line-p-gray
            border-radius: 60px
            @include md
              width: 190px
              height: 30px

            input
              width: calc(100% - 44px)
              padding: 0 0 0 16px
              font-size: .8rem
              line-height: 1
              background-color: transparent
              border: none
              @include md
                width: calc(100% - 34px)
                padding: 0 0 0 12px
              @include sp
                width: 170px

              &::placeholder
                color: #ccc
                font-size: .8rem
                font-weight: 500
                @include md
                  font-size: .7rem
                  transform: translateY(-1px)

              &.side-search-keyword
                height: 100%
                line-height: 42px
                padding-top: 0
                padding-bottom: 0
                @include md
                  height: 30px
                  line-height: 30px

            button
              position: relative
              width:44px
              height:44px
              padding: 0
              background-color: transparent
              border-top-right-radius: 50px
              border-bottom-right-radius: 50px
              @include md
                width: 34px
                height: 30px
                transform: translateY(0px)
              @include sp
                width: 40px
                border-left: none

              img
                width: 24px
                height: auto
                @include md
                  width: 20px

        .nav-btn
          display: flex
          flex-direction: row
          @include md
            justify-content: flex-end
            gap: 20px
            margin: 0
            padding: .7rem .5rem

          .nav-mypage
            width: 56px
            height: 50px
            color: $main-text
            display: flex
            justify-content: center
            align-items: center
            @include md
              width: auto
              height: auto
              margin: 0

            a
              text-align: center
              @include md
                display: flex
                flex-direction: row

              &:hover
                @include sp
                  color: $main-color

              img
                width: 20px
                height: auto
                margin: 0 auto
                @include md
                  width: 14px
                  margin-right: 4px
                  transform: translateY(1px)
                @include sp
                  width: 24px

                &.bid
                  width: 18px
                  @include md
                    width: 12px
                    margin-right: 5px

                &.bidded
                  width: 18px
                  @include md
                    width: 13px
                    margin-right: 5px
                    transform: translateY(1px)

              span
                width: 100%
                display: block
                color: $main-text
                font-size: 10px
                font-weight: 600
                text-align: center
                line-height: 1
                @include md
                  font-size: .8rem
                  font-weight: 600
                @include sp
                  display: none


/* ヘッダー
 *==========================================


/* ---------------------------
 *SPハンバーガーメニュー
 *-----------------------------

header .h-top p.btnMenu
  position: relative
  width: 14vw
  height: 14vw
  flex: 0 0 14vw

  span.ham
    &::before
      content: ""
      display: block
      background-color: #333
      width: 5vw
      height: .45vw
      border-radius: .45vw
      position: absolute
      top: 6.8vw
      left: .6vw
      transform: translateX(3.2vw)
      transition: all 0.3s ease

  &::before, &::after
    content: ""
    display: block
    background-color: #333
    width: 7vw
    height: .45vw
    border-radius: .45vw
    position: absolute
    right: 50%
    transform: translateX(50%)
    transition: all 0.3s ease

  &::before
    top: 4.8vw

  &::after
    bottom: 4.8vw

  &.close
    &::before
      top: 50%
      transform: translate(50%, -50%) rotate(45deg)
      transition: all 0.3s ease

    &::after
      top: 50%
      bottom: auto
      transform: translate(50%, -50%) rotate(-45deg)
      transition: all 0.3s ease

    span.ham
      &::before
        display: none
        transition: all 0.3s ease


/* グロナビ
 *==========================================

/* ---------------------------
 *PC
 *-----------------------------

  header .gNav
    background-color: #fff
    border-bottom: 1px solid #d9d9d9
    display: block !important

    nav
      a
        font-size: 14px
        font-weight: 700

      > ul
        display: flex

        > li
          border-left: 1px solid #d9d9d9
          width: calc(100% / 9)
          height: 50px
          position: relative

          &:last-of-type
            border-right: 1px solid #d9d9d9

          &::after
            content: ""
            display: block
            width: 100%
            height: 2px
            position: absolute
            bottom: -1px
            left: 0
            right: 0
            background-color: transparent
            transition: all 0.08s linear

          &:hover::after
            background-color: #01a7ac

          > a
            display: flex
            align-items: center
            justify-content: center
            width: 100%
            height: 100%
            padding: 5px 10px

          &.nav-black > a
            background-color: #333F48
            color: #fff

          > a:hover
            opacity: 1

          width: calc((100% - 80px - 130px) / 8)

          &.nav-top
            width: 80px

          &.nav-first
            width: 130px

          > ul
            display: none
            position: absolute
            top: 51px
            left: 0
            background-color: rgba(255, 255, 255, 0.9)
            width: 100%
            padding: 15px 0
            z-index: 1

          &.nav-access > ul
            width: 190px

            /* 親メニュ幅より大きく

          &.nav-entry > ul
            width: 125px

            /* 親メニュ幅より大きく

          &.nav-overview > ul
            width: 140px

            /* 親メニュ幅より大きく

          &:hover > ul
            display: block
            animation-name: fade-basic
            animation-duration: .5s

          > ul li a
            display: block
            padding: 7px 15px
            color: #000

/* ---------------------------
 *SP　ヘッダーナビ
 *-----------------------------

header .gNav
  @include sp
    display: none
    position: absolute
    top: 14vw
    left: 0
    right: 0
    width: 100%
    height: 100vh
    background-color: #fbfbfb
    z-index: 100

    nav
      a
        font-size: 16px

      > ul > li
        border-bottom: 1px solid #e7e7e7

        >
          a
            min-height: 50px
            padding: 5px 40px 5px 30px
            position: relative

          p
            font-size: 16px
            display: flex
            align-items: center
            font-weight: 700
            min-height: 50px
            padding: 4vw 10vw 4vw 4vw
            position: relative

        &.account
          display: flex
          flex-direction: row
          justify-content: center
          gap: 3vw
          padding: 3vw 4vw

          .btn
            display: flex
            justify-content: center
            align-items: center
            width: 50%
            padding: 2.8vw 4vw
            color: #fff
            font-size: 3.8vw
            font-weight: 600
            line-height: 1.1
            border-radius: 4px

            &.entry
              background-color: $main-red
            &.login
              background-color: $main-color
            &.mypage
              background-color: $main-red
            &.logout
              color: $main-color
              background-color: #fff
              border: 1px solid $main-color

        &.search
          display: flex
          align-items: center
          padding: .8rem 1rem

          input
            height: 12vw
            width: calc(100% - 12vw)
            padding: 2vw 2vw 2vw 3vw
            border: 1px solid $line-p-gray
            border-right: none
            border-top-left-radius: 4px
            border-bottom-left-radius: 4px

          button
            width: 12vw
            height: 12vw
            background-color: $main-color
            border: 1px solid $main-color
            border-left: none
            border-top-right-radius: 4px
            border-bottom-right-radius: 4px

            img
              width: 6.5vw
              height: auto

        &.nav-black >
          a, p
            background-color: $main-smoke-bg-color
            color: $main-text
            font-size: 3.8vw

        >
          a::after
            content: ""
            display: block
            position: absolute
            top: 50%

          p
            &::before, &::after
              content: ""
              display: block
              position: absolute
              top: 50%

        &:not(.nav-black)
          > ul li a::after
            content: ""
            display: block
            position: absolute
            top: 50%

          > ul li a::after
            width: 8px
            height: 8px
            border-top: #000 2px solid
            border-right: #000 2px solid
            transform: rotate(45deg) translateY(-50%)
            right: 28px

        > a::after
          width: 8px
          height: 8px
          border-top: #000 2px solid
          border-right: #000 2px solid
          transform: rotate(45deg) translateY(-50%)
          right: 28px

        &.nav-black
          > a::after
            border-top-color: $line-gray
            border-right-color: $line-gray

          > p
            &::before, &::after
              background-color: #666

          > ul li
            border-top: none
            display: block
            width: 100%

        > p
          &::before
            width: 16px
            height: 2px
            background-color: #000
            transform: translateY(-50%)
            right: 20px
            @include sp
              width: 3.5vw
              right: 4vw

          &::after
            width: 2px
            height: 16px
            background-color: #000
            transform: translateY(-50%) rotate(0deg)
            transition: all 0.3s ease
            right: 27px
            @include sp
              height: 3.5vw
              right: 5.6vw

        >
          p.close::before
            display: none
            transition: all 0.3s ease

          p.close::after
            transform: translateY(-50%) rotate(90deg)
            transition: all 0.3s ease

          ul
            display: none
            padding: 0
            border-top: 1px solid $line-p-gray
            background-color: #fff

            li
              + li
                border-top: 1px solid $line-p-gray

              &:first-child > a
                border-top: none

              a
                display: flex
                align-items: center
                width: 100%
                color: #797979
                font-size: 3.6vw
                font-weight: 500
                min-height: 40px
                padding: 3.5vw 8vw
                position: relative
                border-top: 1px solid #efefef

                &:after
                  content: ""
                  width: 6px
                  height: 6px
                  border: 0
                  border-top: solid 2px #797979
                  border-right: solid 2px #797979
                  position: absolute
                  top: calc(50% + 1px)
                  right: 20px
                  margin-top: -4px
                  -webkit-transform: rotate(45deg)
                  transform: rotate(45deg)
                  @include sp
                    width: 1.2vw
                    height: 1.2vw
                    right: 5.3vw

          p.ttl
            padding: 0
            &:before,&:after
              display: none
            a
              display: block
              width: 100%
              height: 100%
              padding: 5px 1rem
              color: #fff
              font-weight: 700

        &:not(.nav-black) > ul li a
          padding-left: calc(30px + 1em)

      .line-logo
        width: 100%
        padding: 60px 0
        background-color: #fff
        @include sp
          padding: 10vw
          border-top: 1px solid #efefef

        .cont-wrap
          display: flex
          flex-direction: row
          justify-content: space-between
          align-items: center
          width: 1180px
          max-width: 100%
          margin: 0 auto
          padding: 0 2rem
          @include sp
            width: 100%
            flex-direction: column

          .pct
            display: flex
            justify-content: space-between
            align-items: center
            width: 160px
            height: 100%
            @include sp
              justify-content: center
              width: 60%
              margin: 0 auto 5vw

            a

              &:hover
                opacity: .8

            img
              width: 100%
              height: auto

          .sns
            ul
              display: flex
              flex-direction: row
              gap: 8px

              li
                width: 40px
                height: 40px
                padding: 0

                a
                  display: flex
                  justify-content: center
                  align-items: center
                  width: 100%
                  height: 100%

                  img
                    width: 30px
                    height: auto

                    &.facebook
                      width: 26px
                    &.x
                      width: 22px
                    &.instagram
                      width: 24px

      .line-copyright
        max-width: 100%
        margin: 0 auto
        width: 100%
        padding: 24px 1rem
        background-color: #fff
        border-top: 1px solid $line-p-gray

        .cont-wrap
          display: flex
          flex-direction: row
          justify-content: space-between
          align-items: center
          width: 1180px
          max-width: 100%
          margin: 0 auto
          padding: 0 2rem
          @include sp
            width: 100%
            flex-direction: column

          ul
            display: flex
            justify-content: space-between
            align-items: center
            flex-direction: row
            @include sp
              width: 100%
              flex-direction: column
              padding: 2vw 0 6vw

            li
              width: auto
              padding: 0 1.5rem 0 0
              @include sp
                padding: 2vw 4vw

              a
                padding: 0
                font-size: 13px
                @include sp
                  font-size: 3vw

                &:hover
                  text-decoration: underline

          .copyright
            small
              padding: 0
              font-size: 10px
              font-weight: 400
              @include sp
                font-size: 2vw


/***********************************************************************
 *
 *------------------------------------------------------------------------
 *main
 ***********************************************************************

#main
  display: block
  padding: 0
  @include sp
    padding: 0 0 12vw

  &.stock, &.auction
    padding-bottom: 60px
    @include sp
      padding-bottom: 40px

  #pNav
    padding: 0 1rem
    background-color: #fff
    border-bottom: 1px solid $line-p-gray
    overflow: hidden
    @include md
      padding: 0 1.5rem

    ul
      width: 100%
      max-width: 1280px
      display: flex
      flex-wrap: nowrap
      padding: 10px 0
      margin: 0 auto
      overflow-x: auto
      white-space: nowrap
      @include sp
        width: 100%

      li
        font-size: .75rem
        @include sp
          font-size: 2vw

        &:not(:last-of-type)::after
          content: "/"
          display: inline-block
          margin: 0 .8rem

        a
          color: $main-color
          &:hover
            text-decoration: underline

/* パンくずリスト
 *==========================================

  #main #pNav > ul
    @include sp
      overflow-x: auto
      word-break: keep-all
      white-space: wrap
      -webkit-overflow-scrolling: touch

/***********************************************************************
 *
 *------------------------------------------------------------------------
 *footer
 ***********************************************************************

footer
  position: relative
  background-color: #e8e8e8
  color: #fff
  padding: 0
  @include sp
    padding: 0
    background-color: #fff

  #page_top
    position: fixed
    display: block
    width: 42px
    height: 42px
    right: 1rem
    bottom: 1rem
    background-color: $main-color
    border-radius: 50%
    z-index: 10
    @include sp
      width: 12vw
      height: 12vw

    a
      display: block
      width: 100%
      height: 100%
      text-decoration: none
      cursor: pointer
      z-index: 11

      &:hover
        opacity: .7

      &:before
        content: ''
        width: 6px
        height: 6px
        border: 0
        border-top: solid 2px #fff
        border-right: solid 2px #fff
        position: absolute
        top: calc(50% + 1px)
        left: calc(50% - 4px)
        margin-top: -4px
        transform: rotate(-45deg)

  nav
    width: 1180px
    max-width: 100%
    margin: 0 auto
    padding: 60px 2rem 80px
    @include md
      padding: 60px 2rem 80px
    @include sp
      margin: 0
      padding: 0

    ul li a
      display: inline-block
      color: $main-text
      font-size: 14px
      @include sp
        position: relative
        padding: 5px 1rem

      &[target="_blank"]::after
        content: ""
        display: inline-block
        background: url("../img/common/ic_link_blank_gray.svg") (center / cover) no-repeat
        width: 12px
        height: 12px
        position: relative
        top: 1px
        margin-left: 6px
        @include sp
          content: none
          background: none


    .fNav_pc
      display: flex
      flex-direction: row
      justify-content: center
      gap: 60px
      @include md
        gap: 4vw

      .fNav-1
        width: 77%
        padding: 0 20px 0 0
        @include md
          width: 70%

        ul.list
          display: flex
          flex-direction: row
          flex-wrap: wrap
          justify-content: space-between

          li
            p
              margin: 0 0 .5rem
              color: $main-text
              font-size: .9rem
              font-weight: 700

            ul
              display: flex
              flex-direction: column
              li
                a
                  font-size: .8rem

      .fNav-2
        display: flex
        justify-content: flex-end
        width: 23%
        padding: 0 0 0 40px
        border-left: 1px solid #ccc
        @include md
          width: 20%
          padding: 0 0 0 4vw

        ul
          margin: 0 auto 0 0
          li
            padding: 0 0 .4rem
            p
              a
                inline-block
                font-size: .9rem
                font-weight: 600
                cursor: pointer

    .fNav_sp
      display: none
      flex-direction: column
      ul li a::after
      display: flex
      flex-direction: column
      padding: 0

      ul
        li
          &.bg-gray
            background-color: $main-smoke-bg-color

          p,a
            color: $main-text

    .fNav_sp > ul > li

      &:first-child
        border-top: 1px solid #e7e7e7

      ul a
        &::after
          border-top-color: #666
          border-right-color: #666

        &[target="_blank"]::after

      a
        min-height: 48px
        padding: 4vw 10vw 4vw 4vw
        font-size: 3.8vw
        font-weight: 700
        border-bottom: 1px solid #e7e7e7
        display: flex
        align-items: center
        position: relative

      p
        display: flex
        align-items: center
        position: relative
        min-height: 48px
        padding: 4vw 10vw 4vw 4vw
        font-size: 3.8vw
        font-weight: 700
        border-bottom: 1px solid #e7e7e7

        &::before, &::after
          content: ""
          display: block
          position: absolute
          top: 50%

        &::before
          width: 16px
          height: 2px
          background-color: #666
          transform: translateY(-50%)
          right: 20px
          @include sp
            width: 3.5vw
            right: 4vw

        &::after
          width: 2px
          height: 16px
          background-color: #666
          transform: translateY(-50%) rotate(0deg)
          transition: all 0.3s ease
          right: 27px
          @include sp
            height: 3.5vw
            right: 5.6vw

        &.close::before
          display: none
          transition: all 0.3s ease

        &.close::after
            transform: translateY(-50%) rotate(90deg)
            transition: all 0.3s ease

        ul li a::after
          width: 8px
          height: 8px
          border-top: #666 2px solid
          border-right: #666 2px solid
          transform: rotate(45deg) translateY(-50%)
          right: 22px

      ul, dl
        display: none
        background-color: #fff

      ul a
        color: $main-text
        padding-left: 42px

      dl a
        color: #01a7ac
        padding-left: 42px

        &::after
          display: none
          padding-right: 0

      ul a
        min-height: 48px
        font-size: 3.6vw
        font-weight: 500
        border-bottom: 1px solid #e7e7e7
        display: flex
        align-items: center

        &:after
          content: ""
          width: 6px
          height: 6px
          border: 0
          border-top: solid 2px #797979
          border-right: solid 2px #797979
          position: absolute
          top: calc(50% + 1px)
          right: 5.7vw
          margin-top: -4px
          -webkit-transform: rotate(45deg)
          transform: rotate(45deg)

        &[target="_blank"] span:after
          content: ""
          display: inline-block
          background: url("../img/common/ic_link_blank_gray.svg") no-repeat
          width: 3.5vw
          height: 3.5vw
          position: absolute
          top: calc(50% - 1.7vw)
          margin-left: 1vw

  .line-logo
    width: 100%
    padding: 60px 0
    background-color: #fff
    @include sp
      padding: 10vw

    .cont-wrap
      display: flex
      flex-direction: row
      justify-content: space-between
      align-items: center
      width: 1180px
      max-width: 100%
      margin: 0 auto
      padding: 0 2rem
      @include sp
        width: 100%
        flex-direction: column

      .pct
        display: flex
        justify-content: space-between
        align-items: center
        width: 160px
        height: 100%
        @include sp
          justify-content: center
          width: 64%
          margin: 0 auto 5vw

        a

          &:hover
            opacity: .8

        img
          width: 100%
          height: auto

      .sns
        ul
          display: flex
          flex-direction: row
          gap: 8px

          li
            width: 40px
            height: 40px
            padding: 0

            a
              display: flex
              justify-content: center
              align-items: center
              width: 100%
              height: 100%

              img
                width: 30px
                height: auto

                &.facebook
                  width: 26px
                &.x
                  width: 22px
                &.instagram
                  width: 24px

  .line-copyright
    max-width: 100%
    margin: 0 auto
    width: 100%
    padding: 24px 1rem
    background-color: #333

    .cont-wrap
      display: flex
      flex-direction: row
      justify-content: space-between
      align-items: center
      width: 1180px
      max-width: 100%
      margin: 0 auto
      padding: 0 2rem
      @include sp
        flex-direction: column
        width: 100%
        padding: 0

      ul
        display: flex
        justify-content: space-between
        align-items: center
        flex-direction: row
        @include sp
          width: 100%
          flex-direction: column
          padding: 2vw 0 6vw

        li
          width: auto
          padding: 0 1.5rem 0 0
          @include sp
            padding: 2vw 4vw

          a
            color: #fff
            padding: 0
            font-size: 13px
            @include sp
              font-size: 3vw

            &:hover
              text-decoration: underline

      .copyright
        small
          padding: 0
          color: #fff
          font-size: 10px
          font-weight: 400
          @include sp
            font-size: 2vw
