@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *商品一覧ページ
 ***********************************************************************


/*** オークションタイプ表記 ***/
.auction-type-label
  margin: 0
  padding: 1rem
  text-align: center
  background-color: #000
  @include sp
    padding: 4vw

  .type-tab-wrap
    display: flex
    flex-direction: row
    justify-content: center
    gap: 1rem
    width: 600px
    margin: 0 auto
    padding: 3px
    background-color: #3f3f3f
    border-radius: 50px
    @include sp
      width: 100%
      gap: 1vw

    span,a
      display: inline-block
      padding: .6rem 1.5rem
      color: #fff
      font-size: 1rem
      font-weight: 600
      border-radius: 50px
      line-height: 1.2
      text-align: center
      @include sp
        padding: 2vw
        font-size: 2.8vw

    a
      width: calc(50% - .5rem)
      margin: 0 1rem
      @include sp
        width: calc(50% - .5vw)
        margin: 0

    span
      width: calc(50% - .5rem)
      padding-left: 50px
      color: #4194d3
      line-height: 1
      cursor: default
      background-color: #f5f5f5
      background-image: url(../img/common/icn_check_l-blue.svg)
      background-size: 16px auto
      background-repeat: no-repeat
      background-position: 30px 50%
      z-index: 1
      @include sp
        width: calc(50% - .5vw)
        padding-left: 5vw
        background-size: 3vw auto
        background-position: 4vw 50%



/*** リストタイプ表記 ***/
.list-type-label
  margin: .5rem 0
  padding: 1rem 1rem
  color: #fff
  font-size: .9rem
  background-color: #333
  span
    display: inline-block
    font-weight: 600
    border-radius: 4px
    &:after
      content: ":"
      display: inline-block
      margin: 0 1rem


#main
  h2
    display: flex
    align-items: center
    position: relative
    height: auto

    &.mypage
      background-color: $main-smoke-bg-color

    &::after
      content: ""
      display: block
      position: absolute
      bottom: 0
      right: 0
      width: 100%
      height: 100%

  #list-head
    margin: 40px 0 0
    padding: 0
    @include sp
      margin: 4vw 0 0
      padding: 0

    .search-panel-wrap
      padding: 24px 40px
      background-color: #f5f5f5
      border-radius: 8px
      @include sp
        padding: 0

      .head
        padding: 1rem 1rem 0
        font-size: 1.4rem
        font-weight: 600
        text-align: center
        @include sp
          padding: 4vw 4vw 0
          font-size: 4.5vw

      .contents
        padding: 1.5rem 0 2rem
        @include sp
          padding: 1rem 1rem 2rem

        .keyword
          display: flex
          flex-direction: row
          align-items: center
          padding: 0 0 1rem
          border-bottom: 1px solid $line-p-gray
          @include sp
            flex-direction: column
            padding: 0 .5rem 1.5rem

          &__label
            width: 180px
            padding: .3rem 1rem
            font-weight: 500
            @include sp
              width: 100%
              font-size: 3.5vw
              padding: 1vw 0

          input
            width: calc(100% - 180px)
            max-width: 700px
            margin: 0 0 0 .5rem
            @include sp
              width: 100%
              margin: 0

        .model
          display: flex
          flex-direction: row
          align-items: center
          padding: 1rem 0
          border-bottom: 1px solid $line-p-gray
          @include sp
            flex-direction: column
            padding: .5rem .5rem 1rem

          .model__label
            width: 180px
            padding: .3rem 1rem
            font-weight: 500
            line-height: 1.2
            @include sp
              width: 100%
              padding: 1.7vw 0
              font-size: 3.5vw

          .model__contents
            display: flex
            flex-direction: row
            flex-wrap: wrap
            width: calc(100% - 180px)
            @include sp
              width: 100%

            .label-item
              display: flex
              flex-direction: row
              margin: .15rem .3rem .1rem
              @include sp
                margin: .3rem .5rem 0 0

              input
                transform: scale(1.3)
                @include sp
                  transform: scale(1.3)

              label
                display: flex
                align-items: center
                margin: 0 .5rem
                @include sp
                  font-size: 3vw

        .wrap-btn
          display: flex
          flex-direction: row
          justify-content: center
          align-items: center
          gap: 40px
          margin: 0 auto
          padding: 2rem 1rem 0
          @include sp
            flex-direction: column
            gap: 4vw

          button
            @include sp
              order: 1

          a
            @include sp
              order: 2

          button,a
            cursor: pointer

            &:hover
              opacity: .8

            &.clear
              color: $main-color
              font-size: .9rem
              text-decoration: none
              @include sp
                font-size: 3.2vw

              &:hover
                opacity: 1
                text-decoration: underline

            &.search
              width: 240px
              height: 55px
              margin: 0
              padding: .5rem 1rem
              color: #fff
              font-size: 1rem
              font-weight: 600
              background-color: $main-color
              border-radius: 30px
              @include sp
                width: 60vw
                height: 12vw
                font-size: 3.8vw

    .conditions
      margin: 1rem auto 0
      padding: 20px 40px
      background-color: #f5f5f5
      border-radius: 8px
      @include sp
        padding: 4vw 4vw

      .conditions__label
        display: flex
        flex-direction: row
        align-items: center
        padding: .4rem 1rem 1rem
        border-bottom: 1px solid #e8e8e8
        @include sp
          flex-direction: column
          padding: 0 .5rem 1rem

        .ttl
          width: 100px
          padding: .2rem 0
          font-size: 1rem
          font-weight: 700
          @include sp
            width: 100%
            margin: 0 0 1vw
            font-size: 4vw
            text-align: left

        .elm
          display: flex
          flex-direction: row
          flex-wrap: wrap
          @include sp
            font-size: 3.5vw

          span
            display: inline-block
            margin: 0 .5rem  .5rem 0
            padding: .4rem 1.5rem
            line-height: 1
            background-color: #fff
            border-radius: 20px
            @include sp
              padding: 1.5vw 4vw
              font-size: 3vw


      .results__label
        display: flex
        flex-direction: row
        align-items: baseline
        padding: 1.2rem 1rem .2rem
        @include sp
          padding: 1rem .5rem .2rem

        .ttl
          width: 100px
          font-size: 1rem
          font-weight: 700
          @include sp
            width: auto
            padding: 0 4vw 0 0
            font-size: 4vw

        .elm
          font-size: 1.4rem
          font-weight: 700
          font-family: $font-price
          @include sp
            font-size: 5vw

          span
            display: inline-block
            margin-left: .2rem
            font-size: .9rem
            @include sp
              margin-left: 1vw
              font-size: 3.2vw

  #list
    width: calc(1280px + 2rem)
    max-width: calc(100%)
    margin: 0 auto
    padding: 0 0 20px
    position: relative
    @include sp
      padding: 0 0 7vw

    .container

      .bid-tab-wrap
        display: flex
        flex-direction: row
        justify-content: center
        align-items: end
        gap: 3px
        width: 100%
        margin: 100px 0 40px
        border-bottom: 1px solid $main-text
        @include sp
          margin: 10vw 0 4vw

        .tab-cont,.tab
          position: relative
          display: flex
          justify-content: center
          align-items: center
          width: 380px
          max-width: 50%
          margin: 0
          padding: 0
          border: 1px solid
          @include sp
            height: 14vw

        .tab-cont
          border-color: $main-text
          border-top-left-radius: 8px
          border-top-right-radius: 8px
          border-bottom: none
          height: 62px
          @include sp
            max-width: 50%
            height: 14vw
            margin: 0

          .label
            display: inline-block
            padding: 5px 0 0
            color: $main-text
            font-size: 1rem
            font-weight: 600
            line-height: 1.2
            @include sp
              padding: 1vw 0 0
              font-size: 3.5vw

          &:after
            position: absolute
            content: ""
            bottom: -2px
            width: 100%
            height: 3px
            background-color: #fff
            z-index: 1
            @include sp
              bottom: -1vw
              height: 2vw

        a.tab-cont
          border-color: #c9c9c9
          border-top-left-radius: 8px
          border-top-right-radius: 8px
          border-bottom: none
          height: 56px
          @include sp
            height: 12vw

          .label
            display: inline-block
            padding: 2px 0 0
            color: #c9c9c9
            font-size: 1rem
            font-weight: 600
            line-height: 1.2
            @include sp
              padding: 1vw 0 0
              font-size: 3.5vw

          &:after
            display: none

          &:hover
            opacity: 1
            border-color: $main-text
            transition: all 0.3s ease

            .label
              color: $main-text

          &.active
            z-index: 1
            color: #fff
            font-weight: 600
            border-color: $main-text
            cursor: default

            .label
              color: $main-text


      .display-option
        display: flex
        flex-direction: row
        justify-content: space-between
        margin: 0
        border-bottom: 1px solid $line-gray
        @include sp
          flex-direction: column


        .refine
          position: relative
          display: flex
          align-items: end
          justify-content: flex-start
          flex-wrap: nowrap
          width: auto
          padding: 1rem 0
          @include sp
            justify-content: space-between
            width: 100%

          .sorting
            button.menu_trigger
              position: relative
              width: auto
              min-width: 125px
              margin: 0 2rem 0 0
              padding: .2rem 1.5rem .2rem 1.2rem
              font-size: 1rem
              text-align: left
              background-color: transparent
              @include sp
                padding: 2vw 4vw 2vw 0

              &:after
                content: ""
                position: absolute
                right: 3px
                top: calc(50% - 0px)
                width: 4px
                height: 4px
                border-right: 2px solid #bcbcbc
                border-bottom: 2px solid #bcbcbc
                transform: translateY(-50%) rotate(45deg)

              .option_selected
                @include sp
                  font-size: 3.8vw

            ul.sorting_panel
              display: none
              position: absolute
              top: 55px
              left: 0
              width: 200px
              max-width: calc(100% - 2rem)
              margin: 0
              padding: .8rem 0 1rem
              z-index: 50
              background-color: #fff
              border: 1ps solid #eee
              border-radius:8px
              box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.35)

              &.is-active
                display: block

              .option_item
                margin: 0
                padding: .2rem 1.2rem
                font-size: .9rem
                @include sp
                  font-size: 3.8vw
                &:hover
                  background-color: #eee

                a
                  cursor: pointer

          .check-onsale
            width: auto
            margin: 0
            padding: .2rem 1.2rem
            white-space: nowrap
            @include sp
              padding: 2vw 0

            input
              margin: 0
              transform: scale(1.3) translateY(2px)
              @include sp
                transform: scale(1.8) translateY(.2vw)

            label
              display: inline-block
              margin: 0 .7rem
              white-space: nowrap
              transform: translateY(2px)
              @include sp
                font-size: 3.8vw
                margin: 0 0 0 4vw

          .count
            p
              font-weight: 500
              span
                display: inline-block
                margin: 0 .2rem 0 0
                font-size: 1.4rem
                font-weight: 600

        .switch
          display: flex
          flex-direction: row
          justify-content: flex-end
          width: auto
          padding: 1rem 0
          @include md
            flex-wrap: wrap
          @include sp
            width: 100%
            padding: 4vw 0
            border-top: 1px solid $line-gray

          .dl
            padding: .2rem 1rem
            @include md
              width: 100%
              text-align: right
            @include sp
              margin: 0 0 4vw
              padding: 0 0 4.5vw
              border-bottom: 1px solid $line-gray

            a
              text-decoration: underline
              span
                @include sp
                  font-size: 3.8vw

              &:hover
                text-decoration: none

          .number-switch
            display: flex
            flex-direction: row
            padding: .2rem 1rem
            border-left: 1px solid #999
            @include sp
              justify-content: flex-start
              align-items: baseline
              flex: 1
              padding: 2vw 0
              border-left: none

            .label
              margin: 0 1rem 0 0
              font-weight: 500
              @include sp
                font-size: 3.8vw

            .num
              .btn
                display: inline-block
                margin: 0 .5rem
                padding: 0
                color: #c9c9c9
                font-size: 1rem
                background-color: transparent
                @include sp
                  font-size: 3.8vw

                &.is-active
                  color: #000
                  text-decoration: underline
                &:hover
                  color: #000
                  text-decoration: underline

          .display-switch
            display: flex
            flex-direction: row
            padding: .2rem 1rem
            border-left: 1px solid #999
            @include sp
              padding: 2vw 0 2vw 4vw

            p
              display: flex
              align-items: center
              margin: 0 .6rem
              @include sp
                margin: 0 .6rem

              .btn
                width: 21px
                height: 21px
                padding: 0
                background-color: transparent
                @include sp
                  width: 6vw
                  height: 6vw

                &.row
                  background: url("../img/common/icn_list_switch_row_off.svg") no-repeat
                  background-size: 21px 21px
                  background-position: center
                  background-clip: content-box
                  @include sp
                    background-size: 6vw 6vw

                  &.is-active
                    background: url("../img/common/icn_list_switch_row_on.svg") no-repeat
                    cursor: default
                    @include sp
                      background-size: 6vw 6vw

                  &:hover
                    background: url("../img/common/icn_list_switch_row_on.svg") no-repeat
                    @include sp
                      background-size: 6vw 6vw

                &.panel
                  background: url("../img/common/icn_list_switch_panel_off.svg") no-repeat
                  background-size: 21px 21px
                  background-position: center
                  background-clip: content-box
                  @include sp
                    background-size: 6vw 6vw

                  &.is-active
                    background: url("../img/common/icn_list_switch_panel_on.svg") no-repeat
                    cursor: default
                    @include sp
                      background-size: 6vw auto

                  &:hover
                    background: url("../img/common/icn_list_switch_panel_on.svg") no-repeat
                    @include sp
                      background-size: 6vw auto

      .wrap-btn
        width: 100%
        margin: 2rem 0 0
        text-align: center
        @include sp
          margin: 10vw 0 0

        .list-more
          width: 300px
          height: 55px
          margin: 0 auto
          padding: .5rem 2rem
          color: #fff
          font-size: 1rem
          font-weight: 700
          background-color: #bf2a24
          border-radius: 4px
          @include sp
            height: 60px

      .item-list.row
        margin: 0
        text-align: center

        >ul
          display: flex
          flex-direction: column
          gap: 0
          width: 100%

          >li
            position: relative
            width: 100%
            padding: 0
            margin: 0
            border-bottom: 1px solid $line-gray
            @include sp
              margin: 0 0 1.5 rem
              flex-direction: column

            + li
              margin: 0
              @include sp
                margin: 0

            &:last-child
              border-bottom: 1px solid #ccc

            &.soldout
              position: relative
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                padding: 0
                background-image: url(../img/common/icn_soldout.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10

              .place-bid:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                background: rgba(240, 240, 240, 0.5)
                z-index: 10

            &.nego
              position: relative
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                padding: 0
                background-image: url(../img/common/icn_nego.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10

              .place-bid:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                background: rgba(240, 240, 240, 0.5)
                z-index: 10

            a
              position: relative
              display: grid
              grid-template-columns: 150px 1fr
              grid-template-rows: repeat(5, auto)
              gap: 0 16px
              align-items: start
              width: 100%
              margin: 0
              padding: 1rem 0
              @include sp
                grid-template-columns: 20vw 1fr
                gap: 0 4vw
                padding: 4vw 0

              &:hover
                background-color: $main-bg-gray

              figure
                position: relative
                grid-column: 1 / 2
                grid-row: 1 / 3
                display: flex
                align-items: center
                justify-content: center
                width: 150px
                height: 150px
                border: 1px solid #ccc
                @include sp
                  width: 20vw
                  height: 20vw

                img
                  display: block
                  max-width: 100%
                  max-height: 100%
                  object-fit: contain

                .tab-f
                  span
                    height: 20px
                    padding: 0 14px
                    font-size: .7rem
                    @include sp
                      height: 4vw
                      padding: 0 2vw
                      font-size: 2vw

              .panel-disc
                grid-column: 2 / 3
                grid-row: 1 / 2
                @include sp
                  padding: 0 0 2vw

                .item-name
                  grid-column: 2 / 3
                  grid-row: 1 / 2
                  width: 100%
                  margin: 0
                  padding: 0 .5rem .5rem 0
                  color: $main-color
                  font-weight: 500
                  font-size: .9rem
                  line-height: 1.4
                  border-bottom: 1px solid $line-p-gray
                  @include sp
                    margin: 0
                    padding: 0 0 2vw
                    font-size: 3.5vw

                  .name-title
                    margin: 0 .5rem .2rem 0
                    font-size: .9rem
                    @include sp
                      margin: 0 2vw 1vw 0
                      font-size: 3.5vw

                  .tab-item
                    display: inline-block
                    width: fit-content
                    margin: 0
                    padding: 0 .7rem
                    color: $main-text
                    font-size: .65rem
                    font-weight: 600
                    line-height: 1.1rem
                    border: 1px solid $main-text
                    border-radius: 2px
                    transform: translateY(-0.1rem)
                    @include sp
                      font-size: 2.5vw
                      line-height: 3.8vw

                .current-price
                  grid-column: 2 / 3
                  grid-row: 2 / 3
                  padding: 0
                  line-height: 1.2rem
                  @include sp
                    margin: 0 0 .5vw
                    font-size: 3.6vw
                    line-height: 5.8vw

                  .price-c
                    display: inline-block
                    margin-right: 5px
                    color: #231914
                    font-size: .8rem
                    transform: translateY(-1px)
                    @include sp
                      font-size: 3vw

                  .price-v
                    color: #E50A09
                    font-size: 1.2rem
                    font-weight: 700
                    font-family: $font-price
                    @include sp
                      font-size: 5.8vw

                  .price-u
                    color: #E50A09
                    font-size: 1rem
                    font-weight: 700
                    @include sp
                      font-size: 3vw

                  .tax-u
                    display: inline-block
                    margin-left: 5px
                    font-size: .65rem
                    @include sp
                      font-size: 2.3vw

              .pre-bid
                grid-column: 2 / 3
                grid-row: 2 / 3
                gap: 0
                @include sp
                  gap: 1vw

                .end-v,.bid-v
                  display: flex
                  flex-direction: row
                  justify-content: flex-start
                  align-items: center
                  gap: 1rem
                  margin: 0
                  @include sp
                    margin: 0
                    padding: 0 0 0 6vw
                    background-position: 0 calc(50% + 1px)

                  p
                    width: auto
                    @include sp
                      font-size: 3.5vw

                    span
                      @include sp
                        font-size: 3.5vw

                      &.time
                        display: block


              .product-wrap
                position: relative
                padding: 0 0 44px
                width: calc(100% - 150px)
                min-height: 150px
                border-left: 1px solid $main-p-gray
                @include sp
                  width: calc(100% - 100px)
                  padding: 0

                .item-name
                  position: relative
                  width: 100%
                  margin: 0
                  padding: .5rem 110px .5rem 1rem
                  color: #fff
                  font-size: 1rem
                  font-weight: 500
                  line-height: 1.3
                  background-color: $main-gray
                  border: none
                  @include sp
                    height: auto !important
                    padding: .5rem 1rem .5rem 1rem
                    font-size: .8rem
                    line-height: 1.1rem

                  .tag_status
                    position: absolute
                    top: 1rem
                    right: 1rem
                    margin: 0
                    padding: 0
                    line-height: 1
                    @include sp
                      position: static
                      display: block
                      width: 100%
                      padding: .5rem 0 .3rem
                    > p
                      display: inline-block
                      margin: 0 2px 2px 0
                      padding: 4px 12px 5px
                      font-size: .8rem
                      font-weight: 700
                      line-height: 1
                      border-radius: 20px
                      @include sp
                        padding: 5px 16px 6px
                    .status_recommend
                      color: #fff
                      background-color: #ff0000


                .current-price
                  display: flex
                  flex-direction: row
                  align-items: baseline
                  width: 100%
                  padding: .5rem 1rem
                  background-color: #fff
                  @include sp

                  .price-v
                    display: inline-block
                    margin: 0 .5rem
                    color: #E50A09
                    font-size: 1.4rem
                    font-weight: 700
                    line-height: 1.2
                    white-space: nowrap
                    @include sp
                      font-size: 1.4rem

                dl
                  position: absolute
                  left: 0
                  right: 0
                  bottom: 0
                  display: flex
                  flex-direction: row
                  justify-content: flex-start
                  width: 100%
                  height: 44px
                  margin: 0 auto
                  padding: .5rem 1rem
                  background-color: #f0f0f0
                  @include sp
                    position: static
                    flex-direction: column
                    width: 100%
                    height: auto
                    border-bottom: 1px solid #fff

                  dt
                    width: 180px
                    @include sp
                      width: 100%
                    .bid-l
                      font-weight: 400
                      display: inline-block
                      @include sp
                        width: 4rem
                        font-size: .8rem
                    .bid-v
                      margin: 0 0 0 1rem
                      font-weight: 600
                      display: inline-block
                      @include sp
                        margin: 0 0 0 .5rem


                  dd
                    display: flex
                    flex-direction: row
                    width: calc(100% - 180px)
                    @include sp
                      flex-direction: column
                      width: 100%
                    .end-l
                      width: 140px
                      @include sp
                        padding: 0

                      span.label
                        display: inline-block
                        margin: 0 .5rem 0 0
                        @include sp
                          width: 4rem
                          font-size: .8rem
                      span.value
                        display: inline-block
                        font-size: 1rem
                        font-weight: 700
                        @include sp
                          font-size: .9rem
                      span.limit
                        color: #ff0000
                    .end-v
                      width: calc(100% - 140px)
                      @include sp
                        width: 100%
                        padding: 0

                      span.label
                        display: inline-block
                        margin: 0 .5rem 0 0
                        @include sp
                          width: 4rem
                          font-size: .8rem
                      span.value
                        display: inline-block
                        font-size: 1rem
                        font-weight: 700
                        @include sp
                          font-size: .9rem

                .place-bid
                  grid-row: 1 / 4
                  grid-column: 2 / 3
                  position: relative
                  width: 360px
                  padding: 1rem 1rem calc(46px + 2rem)
                  background-color: #f0f0f0
                  @include md
                    width: 240px
                    max-width: 100%
                  @include sp
                    grid-row: 3 / 4
                    grid-column: 1 / 2
                    width: 100%
                    max-width: 100%
                    border-bottom: 1px solid #fff
                  .ttl
                    margin: 0 0 .5rem
                    font-size: 1.2rem
                    font-weight: 700
                  .price
                    font-size: 1.4rem
                    font-weight: 700
                  input
                    width: 7rem
                    margin: 0 0 0 1rem
                    padding: 5px
                    font-size: 1.4rem
                    font-weight: 700
                    text-align: right
                    &::placeholder
                      color: #ddd
                    &.price-bid-comp
                      background-color: #e5e5e5
                      &::placeholder
                        color: #000

                  ul
                    display: flex
                    flex-direction: row
                    flex-wrap: wrap
                    margin: 1rem 0 1rem

                  ul > li > button
                    display: flex
                    align-items: center
                    margin: 0 5px 5px 0
                    padding: 0 7px 0 0
                    font-size: 1rem
                    background-color: #fff
                    border: 1px solid #CDCBCA
                    border-radius: 30px
                    white-space: nowrap
                    @include sp
                      font-size: 1rem
                    span
                      display: inline-block
                      position: relative
                      width: 20px
                      height: 20px
                      margin: 2px 5px 2px 2px
                      padding: 0 7px
                      color: #fff
                      line-height: 1
                      background-color: $main-red
                      border-radius: 20px
                      &::after
                        content: "+"
                        position: absolute
                        top: .7px
                        left: 5.5px
                        width: 14px
                        height: 14px
                        color: #fff

                  .button-bid
                    display: flex
                    flex-direction: row
                    &.invoice
                      margin: 3rem 0 0
                      @include sp
                        margin: 1rem 0 0
                    button
                      width: calc(100% - 50px - 1rem)
                      height: 55px
                      color: #fff
                      font-size: 1rem
                      font-weight: 500
                      background-color: $main-red
                      border-radius: 4px
                      line-height: 1.2
                      &.invoice
                        width: 100%
                        padding: 1px 25% 3px 10px
                        text-align: right
                        background-image: url(../img/common/icn_download_list.svg)
                        background-repeat: no-repeat
                        background-position: right 10% top 50%
                        @include md
                          text-align: center
                        @include sp
                          padding: 1px 10px 3px 10px
                          text-align: center
                          background-position: right calc(50% - 7.2rem) top 50%
                        @media screen and (max-width: 400px)
                          padding: 1px 46px 3px 10px
                          background-position: right 6% top 50%

                      @include md
                        width: auto
                        min-width: calc(100% - 55px - 1rem)

                      @include sp
                        width: calc(100% - 55px - .5rem)
                    .update
                      position: relative
                      width: 55px
                      height: 55px
                      margin: 0 0 0 1rem
                      padding: 1.5rem 0 0
                      color: $main-red
                      text-align: center
                      background-color: #fff
                      border: 1px solid #CDCBCA
                      border-radius: 30px
                      span
                        font-size: .8rem
                      &::after
                        content: ""
                        display: inline-block
                        background: url("../img/common/icn_update_list.svg") center 8px no-repeat
                        background-size: 20px auto
                        width: 30px
                        height: 30px
                        position: absolute
                        top: 0
                        left: calc(50% - 15px)

                  .other-info-detail
                    position: absolute
                    bottom: 1rem
                    left: 1rem
                    width: calc(100% - 2rem)
                    z-index: 10
                    button
                      position: relative
                      width: 100%
                      height: 46px
                      margin: 0
                      padding: .5rem 1rem
                      color: $main-red
                      font-size: 1rem
                      font-weight: 500
                      background-color: #fff
                      border: 2px solid $main-red
                      border-radius: 30px
                      &::after
                        position: absolute
                        top: calc(50% - 4px)
                        right: 15px
                        display: block
                        width: 10px
                        height: 10px
                        padding: 0
                        color: $main-red
                        font-size: 16px
                        font-weight: 900
                        font-family: "Material Icons"
                        content: "\e5cc"
                        line-height: 0.6

            .btn-foreground-wrap
              bottom: 1rem
              @include sp
                bottom: 4vw

          > li.soldout
            a
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                padding: 0
                background-image: url(../img/common/icn_soldout.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10


      .item-list.panel
        margin: 0 0 2rem
        padding: 1rem 0 0
        text-align: center
        @include sp
          padding: 4vw 0 0

        ul
          li
            a
              .panel-disc
                .item-name
                  color: $main-color

                  .name-title
                    @include sp
                      display: inline-block
                      font-size: 2.8vw
                      line-height: 1.4
                      margin: 0 2vw 1vw 0


                  .tab-item
                    padding: 1px 8px
                    color: $main-text
                    border: 1px solid $main-text
                    @include sp
                      font-size: 2vw


      .item-list.row-bid
        margin: 0
        text-align: center

        &.no-item
          padding: 15px
          display: flex
          justify-content: center
          align-items: center
          background-color: #fff

          p.no-item-msg
            text-align: center
            font-size: 22px
            font-weight: 700
            color: #000

        ul
          display: flex
          flex-direction: column
          gap: 0
          width: 100%

          >li
            display: grid
            grid-template-columns: 150px 1fr auto
            gap: 1rem
            padding: 1rem 0
            width: 100%
            margin: 0
            border-bottom: 1px solid #ccc
            @include sp
              grid-template-columns: 1fr
              margin: 0 0 1.5 rem
              flex-direction: column
              gap: 0 4vw

            &.soldout
              position: relative
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                padding: 0
                background-image: url(../img/common/icn_soldout.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10

              .place-bid:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                background: rgba(240, 240, 240, 0.5)
                z-index: 10

            &.closed
              position: relative
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                padding: 0
                background-image: url(../img/common/icn_closed.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10

              .place-bid:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                background: rgba(240, 240, 240, 0.5)
                z-index: 10

            &.preauc
              position: relative
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                padding: 0
                background-image: url(../img/common/layer_preauc.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10

              .place-bid:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                background: rgba(240, 240, 240, 0.5)
                z-index: 10

            &.extended
              position: relative
              figure:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                padding: 0
                background-image: url(../img/common/layer_extended.png)
                background-size: contain
                background-position: center
                background-repeat: no-repeat
                z-index: 10

              .place-bid:after
                content: ""
                position: absolute
                top: 0
                left: 0
                width: 100%
                height: 100%
                background: rgba(240, 240, 240, 0.5)
                z-index: 10

            figure
              position: relative
              grid-column: 1 / 2
              grid-row: 1 / 2
              display: flex
              align-items: center
              justify-content: center
              width: 150px
              height: 150px
              border: 1px solid $line-p-gray
              @include sp
                width: calc(92vw - 2px)
                height: auto
                margin: 0 0 4vw

              img
                display: block
                max-width: 100%
                max-height: 100%
                object-fit: contain

              .tab-f
                position: absolute
                top: 3px
                left: 3px
                @include sp
                  top: 2vw
                  left: 2vw
                  flex-direction: row
                  width: auto
                  height: auto

                span
                  display: flex
                  align-items: center
                  justify-content: center
                  height: 20px
                  padding: 0 14px
                  font-size: .7rem
                  font-weight: 600
                  color: #fff
                  line-height: 1
                  background-color: $main-text
                  border-radius: 2px
                  @include sp
                    height: 6vw
                    padding: 0 4vw
                    font-size: 3.5vw

                  &.delivery,&.title-a
                    color: #fff
                    background-color: $main-text

            .item-p-desc
              position: relative
              grid-column: 2 / 3
              grid-row: 1 / 2
              padding: 0
              @include sp
                grid-column: 1 / 2
                grid-row: 2 / 3

              .item-name
                position: relative
                grid-column: 2 / 3
                grid-row: 1 / 2
                width: 100%
                margin: 0
                padding: 0 0 .5rem
                color: $main-color
                font-weight: 500
                font-size: .9rem
                border-bottom: 1px solid $line-p-gray
                @include sp
                  margin: 0
                  padding: 0 0 3vw

                a
                  display: block
                  padding: 0
                  color: $main-color
                  font-size: .9rem
                  font-weight: 500
                  line-height: 1.5
                  @include sp
                    font-size: 3.5vw

                  &:hover
                    opacity: 1
                    background-color: transparent
                    text-decoration: underline

                  .name-title
                    font-weight: 500

                  .tab-item
                    display: inline-block
                    width: fit-content
                    margin: 0 10px
                    padding: 0 .7rem
                    font-size: .65rem
                    font-weight: 600
                    line-height: 1rem
                    border: 1px solid
                    border-radius: 2px
                    transform: translateY(-1px)
                    @include sp
                      margin: 0 2vw
                      padding: 0 4vw
                      font-size: 2.8vw
                      line-height: 3.8vw

              .summary-wrap
                display: flex
                flex-direction: column
                justify-content: flex-start
                gap: .5rem
                padding: .5rem 0 0
                @include sp
                  gap: 2vw 0
                  padding: 2vw 0 4vw

                .desc-p-top
                  display: flex
                  flex-direction: row
                  @include md
                    flex-direction: column
                  @include sp
                    flex-direction: column

                  .price-box
                    display: flex
                    flex-direction: column
                    align-items: flex-start
                    justify-content: flex-start
                    flex: 1
                    gap: 0
                    margin: 0
                    padding: 0

                    .price
                      display: flex
                      flex-direction: row
                      align-items: baseline
                      justify-content: flex-start
                      flex-wrap: nowrap
                      gap: 0
                      width: 100%
                      margin: 0
                      padding: 0
                      font-size: .7rem
                      @include sp
                        width: 100%
                        max-width: 100%
                        min-height: 8vw
                        margin: 0
                        font-size: 3.6vw
                        line-height: 5.8vw

                      .price-c
                        display: inline-block
                        margin-right: 5px
                        color: #231914
                        font-size: .7rem
                        transform: translateY(-1px)
                        @include sp
                          font-size: 3vw

                      .price-v
                        color: #E50A09
                        font-size: 1.2rem
                        font-weight: 700
                        font-family: $font-price
                        text-align: right
                        white-space: nowrap
                        @include sp
                          font-size: 5.8vw
                          text-align: right

                        &.bl
                          color: $main-text

                        &.sm
                          font-size: 1rem
                          @include sp
                            font-size: 4vw

                      .price-u
                        color: #E50A09
                        font-size: .7rem
                        font-weight: 700
                        @include sp
                          font-size: 2.6vw

                        &.bl
                          color: $main-text

                        &.sm
                          font-size: .7rem
                          @include sp
                            font-size: 2.6vw

                        &.thin
                          font-weight: 400

                      .tax-u
                        display: inline-block
                        margin-left: 5px
                        font-size: .7rem
                        @include sp
                          font-size: 3vw

                  .tab-wrap-status
                    display: flex
                    flex-direction: column
                    justify-content: flex-start
                    align-items: center
                    gap: 3px
                    width: auto
                    margin: 0
                    padding: 0 0 0 1rem
                    @include sp
                      flex-direction: row
                      justify-content: flex-start
                      align-items: flex-start
                      width: auto
                      padding: 0

                    li
                      width: 100%
                      margin: 0
                      padding: 2px 7px
                      border: 1px solid $main-color
                      font-size: .8rem
                      font-weight: 600
                      display: flex
                      justify-content: center
                      align-items: center
                      border-radius: 4px
                      @include sp
                        width: auto
                        padding: 1vw 4vw
                        font-size: 3vw

                      &.top
                        padding: 3px 10px
                        color: #fff
                        background-color: $main-red
                        border: 1px solid $main-red

                      &.min-bid
                        padding: 3px 8px
                        color: $main-red
                        border: 1px solid $main-red

                .tab-wrap
                  display: flex
                  flex-direction: row
                  justify-content: flex-start
                  align-items: center
                  gap: 3px
                  width: 100%
                  margin: 0
                  padding: 0

                  li
                    display: flex
                    justify-content: center
                    align-items: center
                    width: auto
                    margin: 0
                    padding: 1px 8px
                    font-size: .6rem
                    font-weight: 500
                    white-space: nowrap
                    border: 1px solid $main-text
                    border-radius: 2px
                    @include sp
                      font-size: 2vw

                    &.tab-main
                      color: #fff
                      background-color: $main-text
                    &.tab-sub
                      border: 1px solid $main-text
                    &.tab-standard
                      color: $main-text
                      border: 1px solid $main-text

                .pre-bid
                  display: flex
                  flex-direction: row
                  flex-wrap: wrap
                  justify-content: flex-start
                  width: calc(100% - 100px)
                  margin: auto auto 0 0
                  padding: 0
                  @include sp
                    width: 100%
                    flex-wrap: wrap
                    padding: 0

                  li
                    display: flex
                    align-items: center
                    justify-content: center
                    min-height: 16px
                    margin: 0
                    padding: 3px 0 3px 1.4rem
                    border: none
                    @include sp
                      justify-content: center
                      padding: 1vw 0 1vw 6vw

                    p
                      width: 100%
                      font-size: .9rem
                      font-weight: 700
                      line-height: 1.1
                      @include sp
                        font-size: 3.5vw

                      span
                        &.date,&.time
                          font-weight: 700

                        &.red
                          color: #E50A09

                        &.end
                          margin-left: .5rem
                          font-weight: 400
                          @include sp
                            margin-left: 2vw
                            font-size: 3.5vw
                            font-weight: 500

                    &.bid-v
                      width: 80px
                      background: url("../img/common/icn_hammer_list.png") no-repeat
                      background-size: 14px auto
                      background-position: left 50%
                      @include sp
                        width: 16vw
                        background-size: 3.5vw auto

                    &.view
                      width: 80px
                      background: url("../img/common/icn_eye_list.svg") no-repeat
                      background-size: 16px auto
                      background-position: left 50%
                      @include sp
                        width: 16vw
                        background-size: 3.5vw auto

                    &.end-v
                      width: auto
                      padding: 0 0 1px 1.4rem
                      line-height: 1
                      background: url("../img/common/icn_clock_list.png") no-repeat
                      background-size: 15px auto
                      background-position: left calc(50% + 0px)
                      @include sp
                        width: 100%
                        background-size: 3.5vw auto

                    &.favo
                      width: 70px
                      background: url("../img/common/icn_favorite.svg") no-repeat
                      background-size: 16px auto
                      background-position: left 50%
                      @include sp
                        width: 16vw
                        background-size: 3.5vw auto

                .btn-foreground-wrap
                  @include sp
                    position: static
                    width: 100%
                    margin: 0
                    padding: 1vw 0 0

            .place-bid
              position: relative
              width: 360px
              max-width: 40vw
              padding: 1.5rem 1rem
              background-color: #f5f5f5
              @include sp
                grid-column: 1 / 2
                grid-row: 3 / 4
                width: 100%
                max-width: 100%
                padding: 6vw 4vw 6vw

              &.history
                .price
                  display: flex
                  flex-direction: row
                  justify-content: space-between
                  align-items: baseline
                  width: 100%
                  margin: 0 0 1rem
                  font-size: 1rem
                  font-weight: 600
                  text-align: left
                  @include sp
                    margin: 0 0 4vw
                    font-size: 4vw

                  .winning-bid-price
                    flex: 1
                    width: calc(100% - 7rem)
                    max-width: calc(100% - 7rem)
                    margin: 0 .5rem 0 0
                    padding: 5px
                    font-size: 1.4rem
                    font-weight: 700
                    font-family: $font-price
                    text-align: right
                    background-color: #e8e8e8
                    border: none
                    border-radius: 4px
                    @include sp
                      width: 40vw
                      padding: 3vw
                      font-size: 5.2vw

                .invoice
                  width: 100%

                  button
                    display: flex
                    justify-content: center
                    align-items: center
                    width: 100%
                    height: 48px
                    color: #fff
                    background-color: $main-text
                    border-radius: 50px
                    @include sp
                      height: 14vw

                    span
                      position: relative
                      display: inline-block
                      padding: 0 26px 0 0
                      font-size: 1rem
                      font-weight: 500
                      @include sp
                        padding: 0 6vw 0 0
                        font-size: 4vw

                      &::after
                        position: absolute
                        top: 3px
                        right: 0
                        content: ""
                        display: inline-block
                        background: url(../img/common/icn_download_w.svg) center no-repeat
                        background-size: 17px auto
                        width: 18px
                        height: 18px
                        @include sp
                          top: 1vw
                          background-size: 4vw auto
                          width: 4vw
                          height: 4vw

              .price
                display: flex
                flex-direction: row
                justify-content: space-between
                align-items: baseline
                width: 100%
                font-size: 1rem
                font-weight: 600
                text-align: left

                .ttl
                  margin: 0 auto 0 0
                  font-size: 1rem
                  font-weight: 700
                  white-space: nowrap
                  @include sp
                    font-size: 4vw

                input
                  flex: 1
                  width: calc(100% - 7rem)
                  max-width: calc(100% - 7rem)
                  margin: 0 .5rem 0 0
                  padding: 5px
                  font-size: 1.4rem
                  font-weight: 700
                  font-family: $font-price
                  text-align: right
                  border: none
                  border-radius: 4px
                  @include sp
                    width: 40vw
                    font-size: 5.8vw

                  &::placeholder
                    color: #ddd
                    font-size: 1.4rem
                    @include sp
                      font-size: 5.8vw

                  &.price-bid-comp
                    background-color: #e5e5e5
                    &::placeholder
                      color: #000

                .unit
                  font-size: 1rem
                  font-weight: 600
                  @include sp
                    font-size: 4vw

              ul.bidding-unit
                display: flex
                flex-direction: row
                flex-wrap: wrap
                gap: 10px 5px
                width: 100%
                margin: 1rem 0 1rem
                @include sp
                  margin: 4vw 0

                li
                  flex: 1
                  margin: 0
                  padding: 0
                  border: none
                  grid-template-columns: 1fr

                  .bid-unit
                    display: flex
                    align-items: center
                    justify-content: space-between
                    width: 100%
                    max-width: 100%
                    margin: 0
                    padding: .2rem .4rem .2rem .2rem
                    font-size: .8rem
                    font-weight: 600
                    font-family: $font-price
                    background-color: #fff
                    border-radius: 4px
                    white-space: nowrap
                    @include sp
                      max-width: 100%
                      padding: 1vw
                      font-size: 3vw

                    span
                      display: inline-block
                      position: relative
                      width: 20px
                      height: 20px
                      margin: 0 5px 0 0
                      padding: 0 7px
                      color: #fff
                      line-height: 1
                      background-color: $main-color
                      border-radius: 20px
                      @include sp
                        width: 5vw
                        height: 5vw
                        margin: 0 1.5vw 0 0

                      &::after
                        content: "+"
                        position: absolute
                        top: 0
                        left: 0
                        display: flex
                        justify-content: center
                        align-items: center
                        width: 100%
                        height: 100%
                        color: #fff
                        font-size: 1rem
                        transform: translateY(-1px)

              .button-bid
                display: flex
                flex-direction: row

                .btn
                  display: flex
                  justify-content: center
                  align-items: center
                  width: 100%
                  height: 55px
                  color: #fff
                  font-size: 1rem
                  font-weight: 500
                  background-color: $main-color
                  border-radius: 50px
                  line-height: 1.2
                  @include sp
                    height: 14vw
                    font-size: 4.6vw

                  &:hover
                    opacity: .8

                  .pct
                    width: 16px
                    height: auto
                    @include sp
                      width: 4.4vw

                  .bid
                    position: relative
                    width: auto
                    display: inline-block
                    padding-left: 14px

                    font-weight: 600

                .update
                  position: relative
                  width: 55px
                  height: 55px
                  margin: 0 0 0 .8rem
                  padding: 1.5rem 0 0
                  color: $main-color
                  text-align: center
                  background-color: #fff
                  border: 1px solid $main-color
                  border-radius: 30px
                  cursor: pointer
                  @include sp
                    width: 14vw
                    height: 14vw
                    margin: 0 0 0 3vw
                    padding: 7vw 0 0
                    border-radius: 30vw

                  &:hover
                    opacity: .8

                  span
                    font-size: .7rem
                    @include sp
                      font-size: 3vw

                  &::after
                    content: ""
                    display: inline-block
                    background: url("../img/common/icn_refresh_blue.svg") center no-repeat
                    background-size: 18px auto
                    width: 22px
                    height: 22px
                    position: absolute
                    top: 6px
                    left: calc(50% - 11px)
                    @include sp
                      background: url("../img/common/icn_refresh_blue.svg") center no-repeat
                      background-size: 4.8vw auto
                      width: 5.5vw
                      height: 5.5vw
                      top: 1.8vw
                      left: calc(50% - 2.75vw)


/*** 封印入札式 ***/
#list-sealed
  background-color: $sealed-bg-color

#main
  #list
    &.sealed

      .item-list.panel
        a
          padding: 0 0 40px
          @include sp
            padding: 0 0 12vw
        .btn-foreground-wrap.sealed


      .item-list.row
        a
          .pre-bid.sealed
            display: flex
            flex-direction: row
            @include sp
              flex-direction: column
              gap: 0

            .end-label
              width: auto
              margin-right: 5px
              font-size: .8rem
              @include sp
                font-size: 3vw

            .end-v
              width: auto

        .btn-foreground-wrap.sealed
          bottom: 1rem
          @include sp
            bottom: 4vw

      .item-list.row-bid
        .summary-wrap
          .pre-bid.sealed
            display: flex
            flex-direction: row
            align-items: center

            @include sp
              flex-direction: column
              gap: 0

            .end-label
              width: auto
              margin-right: 5px
              padding: 0
              font-size: .7rem
              line-height: 1
              @include sp
                font-size: 3vw

            .end-v
              width: auto
              font-size: .8rem


.wrap-btn.pagination
  display: flex
  flex-direction: column
  justify-content: center
  align-items: center
  width: 100%
  margin: 40px 0
  font-family: 'Helvetica Neue', sans-serif

  p
    margin: 0 0 1rem
    font-size: .8rem

  ul
    list-style: none
    display: flex
    flex-direction: row
    gap: 8px
    padding: 0
    margin: 0

    li
      position: relative
      width: auto
      height: 40px
      border: none

      &.prev
        width: 40px
        &:after
          content: ""
          width: 6px
          height: 6px
          border: 0
          border-top: solid 1px #333
          border-right: solid 1px #333
          position: absolute
          top: calc(50% + 1px)
          left: calc(50% - 3px)
          margin-top: -4px
          -webkit-transform: rotate(-135deg)
          transform: rotate(-135deg)

      &.next
        width: 40px
        &:after
          content: ""
          width: 6px
          height: 6px
          border: 0
          border-top: solid 1px #333
          border-right: solid 1px #333
          position: absolute
          top: calc(50% + 1px)
          left: calc(50% - 6px)
          margin-top: -4px
          -webkit-transform: rotate(45deg)
          transform: rotate(45deg)

      a
        display: block
        justify-content: center
        align-items: center
        width: 100%
        height: 100%
        padding: 6px 14px
        color: $main-gray
        font-weight: 400
        text-decoration: none
        border: 1px solid #ccc
        transition: all 0.2s ease
        background-color: #fff

        &:hover
          background-color: #f0f0f0
          border-color: #999

        &.active
          background-color: $main-gray
          color: #fff
          border-color: $main-gray
