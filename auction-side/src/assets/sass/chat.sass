@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *チャット
 ***********************************************************************

/*
 *==========================================

#chat
  #main
    padding: 0

#chat-contact
  background-color: #f7f7f7

  .container
    width: 1280px
    max-width: 100%
    margin: 0 auto
    padding: 0
    position: relative
    @include sp
      width: 100%
      max-width: 100%
      margin: 0

    .tab-wrap
      display: flex
      flex-direction: row
      width: 100%

      button
        width: 50%
        padding: 1.3rem 1rem
        @include sp
          padding: 1rem .7rem

        span
          display: inline-block
          font-size: 1.6rem
          font-weight: 700
          line-height: 1.4
          @include sp
            font-size: 1rem

        &.is-active
          color: #fff
          background-color: $main-color
        &.is-active:hover
          cursor: default
          background-color: $main-color

        &:hover
          background-color: #c7e3c1
          opacity: 1

    .chat-shipping
      display: none

    .chat-item,.chat-shipping
      position: relative
      width: 100%
      margin: 0
      padding: 2rem 2rem
      border: none
      @include sp
        margin: 0 auto
        padding: 4vw 4vw 17vw

      h3
        margin: 1rem 0 1rem
        font-size: 1.4rem
        font-weight: 700
        text-align: center
        @include sp
          margin: 4vw 0
          font-size: 3.8vw

      p.note
        font-size: .9rem
        font-weight: 600
        text-align: center
        @include sp
          font-size: 3vw

      .chat-head
        width: 1000px
        max-width: 100%
        margin: 2.5rem auto 2.5rem
        padding: 1.5rem 3rem
        background-color: #fff
        border: none
        border-radius: 4px
        @include sp
          width: 100%
          margin: 4vw 0
          padding: 6vw 4vw 5vw

        .label
          margin: 0 0 .2rem
          font-size: .9rem
          font-weight: 700
          @include sp
            margin: 0 0 1vw
            font-size: 3.5vw

        .item-desc
          display: flex
          flex-direction: row
          align-items: flex-first
          justify-content: space-between
          @include sp
            flex-direction: column

          .item-name-b

            .name
              margin: 0
              font-size: 1.1rem
              font-weight: 400
              line-height: 1.3
              @include sp
                margin: 0 0 3vw
                font-size: 3.8vw

          .back-item-detail
            padding-left: 2rem
            @include sp
              margin: 2vw 0 2vw
              padding: 0

            button
              width: 160px
              height: 42px
              display: flex
              align-items: center
              justify-content: center
              padding: 0
              background-color: $main-color
              border: 1px solid $main-color
              border-radius: 50px
              @include sp
                width: 60vw
                height: 11vw
                margin: 0 auto
                border-radius: 20vw

              span
                display: inline-block
                color: #fff
                font-size: .8rem
                font-weight: 600
                line-height: 1
                white-space: nowrap
                transform: translateY(-1px)
                @include sp
                  font-size: 3.5vw

              &:hover
                background-color: #fff
                span
                  color: $main-color

      .chat-body
        box-sizing: border-box
        width: 1000px
        max-width: 100%
        margin: 0 auto
        padding: 0 0 2rem
        @include sp
          width: 100%
          margin: 4vw auto
          padding: 0

        .chat-body-head
          display: flex
          flex-direction: row
          justify-content: space-between
          align-items: center
          padding: .8rem 1rem
          background-color: #333
          border-top-left-radius: 4px
          border-top-right-radius: 4px
          @include sp
            padding: 3vw 4vw

          .title
            padding: 0 .5rem
            @include sp
              padding: 0
            p
              color: #fff
              font-size: 1.2rem
              font-weight: 600

          .btn-wrap
            button
              position: relative
              display: flex
              align-items: center
              justify-content: center
              width: 80px
              height: 38px
              padding: 0 0 0 16px
              color: $main-text
              text-align: center
              background-color: #efefef
              border-radius: 4px
              cursor: pointer
              @include sp
                width: 20vw
                height: 10vw
                padding: 0 0 0 4.2vw

              span
                font-size: .9rem
                font-weight: 600
                line-height: 1
                @include sp
                  font-size: 3.5vw

              &:after
                content: ""
                display: inline-block
                background: url(../img/common/icn_refresh_bk.svg) no-repeat
                background-size: 14px auto
                background-position: center
                width: 16px
                height: 16px
                position: absolute
                top: calc(50% - 8px)
                left: 14px
                @include sp
                  background-size: 3.5vw
                  width: 4vw
                  height: 4vw
                  top: calc(50% - 2vw)
                  left: 3.6vw

              &:hover
                background-color: #ccc

        .chat-body-wrap
          box-sizing: border-box
          width: 100%
          max-height: 680px
          padding: 3rem 3rem
          overflow-y: scroll
          scrollbar-width: 2px
          background-color: #e1eaf0
          border: 1px solid $line-blue
          border-bottom-left-radius: 4px
          border-bottom-right-radius: 4px
          @include sp
            max-height: 800px
            padding: 7vw 4vw

          &::-webkit-scrollbar
            width: 3px
          &::-webkit-scrollbar-thumb
            background-color: #ccc
            border-radius: 5px

          .chat-body-detail
            margin-top: 1rem

            &.question
              width: calc(100% - 100px)
              margin: 50px 0 0 auto
              @include sp
                width: 80%
                margin: 4vw 0 0 auto

              &:first-of-type
                margin-top: 0

            &.answer
              width: calc(100% - 200px)
              margin: 50px auto 0 0
              @include sp
                width: 80%
                margin: 4vw auto 0 0

            &:first-of-type
              margin-top: 0

            .chat-body-detail
              &-q
                color: $main-color
                font-weight: 600
                font-size: .9rem
                text-align: right
                @include sp
                  font-size: 3.5vw

              &-text
                box-sizing: border-box
                width: auto
                margin: .2rem 0 0
                padding: 1.5rem 2rem
                border-radius: 4px
                font-size: .9rem
                line-height: 1.6
                @include sp
                  padding: 4vw
                  font-size: 3.2vw

                &.user
                  background-color: #bfd4e5

                &.seller
                  background-color: #fafafa

                &.concealed
                  color: #849db1
                  font-weight: 600
                  background-color:#bfd4e5


              &-a
                color: $main-text
                font-weight: 600
                font-size: .9rem
                @include sp
                  font-size: 3.5vw

      .chat-foot
        box-sizing: border-box
        width: 1000px
        max-width: 100%
        margin: 0 auto
        padding: 0
        @include sp
          padding: 0

        .chat-form
          display: flex
          flex-direction: column
          flex-wrap: wrap
          align-items: center
          justify-content: space-between
          @include sp

          .chat-form-textarea
            box-sizing: border-box
            width: 100%
            padding: 0
            @include sp
              width: 100%
              padding-right: 0

            textarea
              -webkit-appearance: none
              width: 100%
              min-height: 110px
              padding: 11px 12px
              color: inherit
              font-size: .9rem
              resize: none
              background-color: #fff
              border: 1px solid $line-blue
              border-radius: 4px
              @include sp
                min-height: 50vw
                padding: 2vw 3vw
                font-size: 3.8vw

              &::placeholder
                color: #ccc
                font-size: 1rem
                @include sp
                  font-size: 3.8vw

          .chat-form-note
            width: 100%
            padding: 0 .2rem
            @include sp
              padding: 0 1vw

            ul
              li
                color: $main-red
                font-size: .8rem
                font-weight: 500

          .chat-form-btnWrap
            width: 100%
            margin: 0 auto
            @include sp
              width: 100%
              margin: 0

            .chat-form-btn
              display: flex
              position: relative
              align-items: center
              justify-content: center
              width: 380px
              height: 70px
              margin: 2rem auto 3rem
              color: #fff
              font-weight: 700
              font-size: 1rem
              line-height: 1.2
              background-color: $main-color
              border-radius: 40px
              transition: all ease 0.7s
              @include sp
                width: 100%
                max-width: 100%
                height: 16vw
                margin: 4vw auto

              &.clear
                width: 280px
                height: 60px
                display: flex
                justify-content: center
                align-items: center
                margin: 0 auto 3rem
                color: #427fae
                font-size: 1rem
                font-weight: 700
                background-color: #fff
                border: 1px solid #427fae
                border-radius: 50px
                @include sp
                  width: 100%
                  max-width: 100%
                  height: 14vw
                  margin: 4vw auto
                  font-size: 4vw

              &:hover
                opacity: 0.8

              &.keep
                background: #364A81

              &.report
                background: #E70012
                color: #fff

              &.reset
                background: #666666

              &.submit
                background: #E04910

              &.lt_gray
                background: #999999 !important
