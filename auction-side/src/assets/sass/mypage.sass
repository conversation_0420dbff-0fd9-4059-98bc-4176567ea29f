@charset "utf-8"
@import "./mixin"
/***********************************************************************
 *
 *------------------------------------------------------------------------
 *マイページ
 ***********************************************************************

/* Nav
 *==========================================

#main
  #mypage-head
    padding: 0

    .container
      width: 100%
      padding: 0

      .nav-wrap
        display: flex
        flex-direction: row
        justify-content: center
        gap: 0
        width: 100%
        max-width: 100%
        margin: 0 auto
        padding: 0 2rem
        border-bottom: 1px solid $line-p-gray

        .nav-content
          width: 220px
          max-width: 100%
          height: 80px
          margin: 0
          padding: 0
          border-radius: 0
          @include sp
            width: 25%
            height: 18vw
            min-height: 12vw
            margin: 0

          &:hover
            opacity: 1

            span
              &.favorite
                background-image: url(../img/common/icn_mypage_nav_favorite.svg)
              &.bidding
                background-image: url(../img/common/icn_mypage_nav_bid.svg)
              &.winning-history
                background-image: url(../img/common/icn_mypage_nav_winning-history.svg)
              &.account
                background-image: url(../img/common/icn_mypage_nav_account.svg)
              &.upload
                margin-left: 3px
                background-image: url(../img/common/icn_mypage_nav_upload.svg)
                background-size: 13px auto
                background-position: center 2px

            .label
              color: $main-text

          a
            position: relative
            display: flex
            justify-content: center
            align-items: center
            flex-direction: row
            width: 100%
            height: 100%
            padding: 0
            @include sp
              flex-direction: column
              padding: 4vw 0 3vw

            span
              display: inline-block
              content: ""
              background-position: center
              background-size: 20px auto
              background-repeat: no-repeat
              width: 20px
              height: 20px
              transition: all 0.3s ease
              @include sp
                top: 0
                left: calc(50% - 2.75vw)
                width: 5.5vw
                height: 5.5vw
                background-size: 5vw auto

              &.favorite
                background-image: url(../img/common/icn_mypage_nav_favorite_pgr.svg)
              &.bidding
                background-image: url(../img/common/icn_mypage_nav_bid_pgr.svg)
              &.winning-history
                background-image: url(../img/common/icn_mypage_nav_winning-history_pgr.svg)
              &.account
                background-image: url(../img/common/icn_mypage_nav_account_pgr.svg)
              &.upload
                margin-left: 3px
                background-image: url(../img/common/icn_mypage_nav_upload_pgr.svg)
                background-size: 13px auto
                background-position: center 2px
                @include sp
                  position: absolute
                  top: 4.3vw
                  left: 13vw
                  background-size: 3vw auto

            .label
              display: inline-block
              padding: 0 0 0 .5rem
              color: #c9c9c9
              font-size: 1rem
              font-weight: 600
              line-height: 1.2
              transition: all 0.3s ease
              @include sp
                padding: 2vw 0 0
                font-size: 2.2vw

          &.active
            background-color: transparent
            border-bottom: 3px solid $main-text

            a
              position: relative
              cursor: default
              pointer-events: none
              &:hover
                opacity: 1

              .favorite
                background-image: url(../img/common/icn_mypage_nav_favorite.svg)
              .bidding
                background-image: url(../img/common/icn_mypage_nav_bid.svg)
              .winning-history
                background-image: url(../img/common/icn_mypage_nav_winning-history.svg)
              .account
                background-image: url(../img/common/icn_mypage_nav_account.svg)
              .upload
                background-image: url(../img/common/icn_mypage_nav_upload.svg)

              .label
                color: $main-text

  #mypage-form
    margin: 0 0 60px
    padding: 0 0 1rem
    @include sp
      margin: 0 0 40px
      padding: 0 0 1rem

    .container
      .assessment
        width: 980px
        max-width: 100%
        margin: 0 auto 1rem
        padding: 2rem
        background-color: $main-bg-color
        border: 1px solid $line-gray
        @include sp
          width: 100%
          padding: 7vw 4vw

        .intro
          margin: 0 0 2rem

          p
            font-size: .9rem
            font-weight: 600
            text-align: center
            @include sp
              font-size: 3.5vw
              text-align: left

        .ttl-assessment-flow
          text-align: center
          margin: 1rem 0
          font-size: 1.1rem
          font-weight: 700
          @include sp
            font-size: 4vw

        .cont-assessment-flow
          display: flex
          flex-direction: row
          flex-wrap: wrap
          margin: 1rem 0
          @include sp
            flex-direction: column

          .box-flow
            width: calc((100% - 60px) / 3)
            padding: 1.5rem 1.2rem
            background-color: #fff
            @include sp
              width: 100%
              padding: 5vw 6vw

            .pri-item
              display: flex
              flex-direction: row
              margin: 0 0 .5rem

              .num
                display: flex
                align-items: center
                justify-content: center
                width: 40px
                height: 40px
                font-size: 1rem
                font-family: $font-price
                background-color: $main-color
                border-radius: 2px
                @include sp
                  font-size: 5vw

                span
                  color: #fff
                  font-size: 1rem
                  font-weight: 600
                  @include sp
                    font-size: 3vw

              .text-detail
                display: flex
                flex-direction: row
                padding: 0 0 0 10px
                align-items: center
                flex: 1

                p
                  font-size: .8rem
                  font-weight: 600
                  line-height: 1.3
                  @include sp
                    font-size: 3.5vw

                .btn.assessment-info
                  width: 18px
                  min-width: 18px
                  height: 18px
                  margin: 0 0 0 5px
                  padding: 0
                  background-color: transparent

                  img
                    width: 100%
                    height: auto

            .ann
              color: #ff0000
              font-size: .7rem
              text-align: center
              @include sp
                font-size: 3vw

          .arrow-assessment-flow
            display: flex
            justify-content: center
            align-items: center
            width: 30px
            height: auto
            @include sp
              width: 100%
              height: 24px

            span
              display: inline-block
              width: 14px
              height: 20px
              @include sp
                width: 20px
                height: 14px
                transform: translateY(-25%)

              &::after
                content: ""
                display: inline-block
                width: 0
                height: 0
                border-top: 10px solid transparent
                border-bottom: 10px solid transparent
                border-left: 14px solid $main-color
                @include sp
                  border-top: 14px solid $main-color
                  border-right: 10px solid transparent
                  border-left: 10px solid transparent
                  border-bottom: none

        .btn-wrap
          width: 100%
          margin: 2rem 0 1rem
          text-align: center

          button.dl-doc
            width: auto
            height: 60px
            margin: 0 auto
            padding: 0 3rem
            background-color: $main-text
            border-radius: 50px
            @include sp
              width: 100%
              height: 14vw

            span
              position: relative
              display: inline-block
              padding: 0 26px 0 0
              color: #fff
              font-size: 1rem
              font-weight: 500
              @include sp
                padding: 0 6vw 0 0
                font-size: 3.8vw

              &::after
                position: absolute
                top: 3px
                right: 0
                content: ""
                display: inline-block
                background: url(../img/common/icn_download_w.svg) center no-repeat
                background-size: 17px auto
                width: 18px
                height: 18px
                @include sp
                  top: 1vw
                  background-size: 4vw auto
                  width: 4vw
                  height: 4vw
