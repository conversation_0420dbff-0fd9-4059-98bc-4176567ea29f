import {Amplify} from 'aws-amplify'

// Use auction-specific Cognito User Pool environment variables
const userPoolId = import.meta.env.VITE_AUCTION_USER_POOL_ID
const clientId = import.meta.env.VITE_AUCTION_CLIENT_ID

if (userPoolId && clientId) {
  Amplify.configure({
    Auth: {
      Cognito: {
        userPoolClientId: clientId,
        userPoolId: userPoolId,
        region: 'ap-northeast-1',
        mandatorySignIn: true,
        loginWith: {
          username: true,
          email: true,
          phone: false,
        },
      },
    },
  })
} else {
  console.error('❌ Missing required auction Cognito configuration:', {
    userPoolId,
    clientId,
  })
}

export default Amplify
