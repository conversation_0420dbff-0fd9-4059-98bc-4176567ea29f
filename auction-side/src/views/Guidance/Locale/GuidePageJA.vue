<script setup>
  import {defineAsyncComponent, onMounted} from 'vue'
  import {scrollToAnker} from '../../../composables/common'
  const PageTopLink = defineAsyncComponent(() => import(/* webpackChunkName: "PageTopLink" */ '../../../components/parts/PageTopLink.vue'))

  onMounted(() => {
    console.log('mounted JA')
    $('a[href^="#"]').click(function () {
      const href = $(this).attr('href')
      const hash = href === '#' || href === '' ? 'html' : href
      scrollToAnker(hash)
      return false
    })
  })
</script>
<template>
  <div>
    <PageTopLink />
    <h1 class="mb0">オークションご利用ガイド</h1>
    <section id="beginner">
      <div class="container">
        <div class="table-contents">
          <h2>ご利用の流れ</h2>
          <div class="list-wrap">
            <ul>
              <li>
                <a href="#guide-ta01"><span class="stp">STEP 1</span>新規会員登録</a>
              </li>
              <li>
                <a href="#guide-ta02"><span class="stp">STEP 2</span>WEB入札</a>
              </li>
              <li>
                <a href="#guide-ta03"><span class="stp">STEP 3</span>落札結果</a>
              </li>
              <li>
                <a href="#guide-ta04"><span class="stp">STEP 4</span>代金のお支払い</a>
              </li>
              <li>
                <a href="#guide-ta05"><span class="stp">STEP 5</span>商品の引渡し</a>
              </li>
            </ul>
          </div>
        </div>
        <ul class="step">
          <li class="step-1" id="guide-ta01">
            <h3><em>STEP 1</em>新規会員登録</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li><span class="sym">01.</span>ヘッダーメニューの『会員について』にある『新規会員登録』よりお申し込みください。</li>
                    <li><span class="sym">02.</span>登録完了後、当社よりメールでご連絡いたします。</li>
                    <li>
                      <span class="sym">03.</span
                      >ヘッダーメニュー右上のログインボタンより、ご登録いただきましたメールアドレス・パスワードにてログインしてください。
                    </li>
                  </ul>
                  <ul class="ann">
                    <li>※登録には、1週間ほどお時間をいただくことがございます。</li>
                    <li>※日本の法人様は、古物商許可証が必要です。</li>
                  </ul>
                </div>
              </div>
            </div>
          </li>

          <li class="step-2" id="guide-ta02">
            <h3><em>STEP 2</em>WEB入札</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li><span class="sym">01.</span>ログインして、オークション内容をご確認ください。</li>
                    <li><span class="sym">02.</span>ご希望の台数と入札単価（米ドル）をご入力ください。</li>
                  </ul>
                  <ul class="ann">
                    <li>
                      ※オークションはせり上げと封印の二種類ございます。<br />
                      ⚫︎せり上げオークション：最高価格で落札が決定<br />
                      ⚫︎封印オークション：優先順位【入札単価⇒入札台数⇒入札時刻】で落札が決定
                    </li>
                    <li>※入札単価は、税抜価格です。</li>
                    <li>※お気に入り登録して、マイページより一括でご入札することもできます。</li>
                  </ul>
                </div>
              </div>
            </div>
          </li>

          <li class="step-3" id="guide-ta03">
            <h3><em>STEP 3</em>落札結果</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li><span class="sym">01.</span>マイページより、落札結果をご確認ください。</li>
                  </ul>
                  <ul class="ann">
                    <li>※ご入札分の落札結果は、メール連絡いたします。</li>
                    <li>※インボイスまたは請求書は、当社よりメールで送付いたします。</li>
                    <li>※入札していないオークションの落札結果につきましては、ログイン後『オークション結果』にて公開しております。</li>
                  </ul>
                </div>
              </div>
            </div>
          </li>

          <li class="step-4" id="guide-ta04">
            <h3><em>STEP 4</em>代金のお支払い</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li><span class="sym">01.</span>（請求書受領後）3営業日以内に代金をお振込みください。</li>
                  </ul>
                  <ul class="ann">
                    <li>※振込先口座情報は、インボイスまたは請求書に記載しております。</li>
                    <li>※円でお支払いの場合、為替レートは、オークション開始までに『お知らせ』にてご案内いたします。</li>
                  </ul>
                </div>
              </div>
            </div>
          </li>

          <li class="step-5" id="guide-ta05">
            <h3><em>STEP 5</em>商品の引渡し</h3>
            <div class="conts">
              <div class="outline">
                <div class="out-img"></div>
                <div class="out-txt">
                  <ul class="flow">
                    <li><span class="sym">01.</span>当社口座への入金を確認次第、商品を出荷いたします。</li>
                  </ul>
                  <ul class="ann">
                    <li>※海外送金の場合、入金確認に時間がかかることがございます。</li>
                    <li>※商品を発送後、当社よりご連絡いたします。</li>
                  </ul>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>
