<template>
  <div class="item_d-main-visual">
    <div class="slider_wrap">
      <!-- Main slider -->
      <div class="my-gallery slider-for">
        <figure v-for="(image, index) in images" :key="index" class="slide-item">
          <a :href="image.large" :data-size="image.size" @click="openLightbox(index, $event)">
            <img :src="image.large" :alt="`Product image ${index + 1}`" />
          </a>
        </figure>
      </div>

      <!-- Thumbnail navigation -->
      <ul class="slider-nav">
        <li v-for="(image, index) in images" :key="index" class="thumbnail-item">
          <img :src="image.thumb" :alt="`Thumbnail ${index + 1}`" />
        </li>
      </ul>
    </div>

    <!-- PhotoSwipe Lightbox -->
    <div class="pswp" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="pswp__bg"></div>
      <div class="pswp__scroll-wrap">
        <div class="pswp__container">
          <div class="pswp__item"></div>
          <div class="pswp__item"></div>
          <div class="pswp__item"></div>
        </div>
        <div class="pswp__ui pswp__ui--hidden">
          <div class="pswp__top-bar">
            <div class="pswp__counter"></div>
            <button class="pswp__button pswp__button--close" title="Close (Esc)"></button>
            <div class="pswp__preloader">
              <div class="pswp__preloader__icn">
                <div class="pswp__preloader__cut">
                  <div class="pswp__preloader__donut"></div>
                </div>
              </div>
            </div>
          </div>
          <button class="pswp__button pswp__button--arrow--left" title="Previous (arrow left)"></button>
          <button class="pswp__button pswp__button--arrow--right" title="Next (arrow right)"></button>
          <div class="pswp__caption">
            <div class="pswp__caption__center"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {ref, onMounted, onUnmounted, nextTick} from 'vue'
  import img01 from '@/assets/img/stock/01.jpg'
  import img02 from '@/assets/img/stock/02.jpg'
  import img03 from '@/assets/img/stock/03.jpg'
  import img04 from '@/assets/img/stock/04.jpg'
  import img05 from '@/assets/img/stock/05.jpg'
  import img06 from '@/assets/img/stock/06.jpg'
  import img07 from '@/assets/img/stock/07.jpg'
  import img08 from '@/assets/img/stock/08.jpg'
  import img09 from '@/assets/img/stock/09.jpg'

  // Props
  const props = defineProps({
    images: {
      type: Array,
      default: () => [
        {large: img01, thumb: img01, size: '600x600'},
        {large: img02, thumb: img02, size: '600x600'},
        {large: img03, thumb: img03, size: '600x600'},
        {large: img04, thumb: img04, size: '600x600'},
        {large: img05, thumb: img05, size: '600x600'},
        {large: img06, thumb: img06, size: '600x600'},
        {large: img07, thumb: img07, size: '600x600'},
        {large: img08, thumb: img08, size: '600x600'},
        {large: img09, thumb: img09, size: '600x600'},
      ],
    },
  })

  let photoswipe = null

  const waitForGalleryElements = () => {
    const sliderFor = document.querySelector('.slider-for')
    const sliderNav = document.querySelector('.slider-nav')

    if (sliderFor && sliderNav && typeof $ !== 'undefined' && $.fn.slick) {
      if (!$(sliderFor).hasClass('slick-initialized')) {
        try {
          // Your exact code from gallery20211111.js
          $('.slider-for').slick({
            asNavFor: '.slider-nav',
            autoplay: false,
            arrows: true,
            infinite: true,
          })

          $('.slider-nav').slick({
            slidesToShow: 7,
            slidesToScroll: 1,
            asNavFor: '.slider-for',
            focusOnSelect: true,
            responsive: [
              {
                breakpoint: 767,
                settings: {
                  slidesToShow: 4,
                },
              },
            ],
          })
        } catch (error) {
          console.error('❌ Error initializing sliders:', error)
        }
      }
    } else {
      setTimeout(waitForGalleryElements, 100)
    }
  }

  const initSliders = async () => {
    await nextTick()
    waitForGalleryElements()
    setTimeout(waitForGalleryElements, 500)
    setTimeout(waitForGalleryElements, 1000)
  }

  const destroySliders = () => {
    try {
      const sliderFor = document.querySelector('.slider-for')
      const sliderNav = document.querySelector('.slider-nav')

      if (sliderFor && $(sliderFor).hasClass('slick-initialized')) {
        $(sliderFor).slick('unslick')
        console.log('🧹 Main slider destroyed')
      }
      if (sliderNav && $(sliderNav).hasClass('slick-initialized')) {
        $(sliderNav).slick('unslick')
        console.log('🧹 Thumbnail slider destroyed')
      }
    } catch (error) {
      console.error('Error destroying sliders:', error)
    }
  }

  const openLightbox = (index, event) => {
    event.preventDefault()
    window.open(props.images[index].large, '_blank')
  }

  onMounted(() => {
    initSliders()
  })

  onUnmounted(() => {
    destroySliders()
    if (photoswipe) {
      photoswipe.destroy()
      photoswipe = null
    }
  })
</script>

<style scoped>
  :deep(.slick-slider) {
    position: relative;
    display: block;
    box-sizing: border-box;
    user-select: none;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
  }

  :deep(.slick-list) {
    position: relative;
    display: block;
    overflow: hidden;
    margin: 0;
    padding: 0;
  }

  :deep(.slick-track) {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    margin-left: auto;
    margin-right: auto;
  }

  :deep(.slick-slide) {
    display: none;
    float: left;
    height: 100%;
    min-height: 1px;
  }

  :deep(.slick-slide.slick-loading img) {
    display: none;
  }

  :deep(.slick-initialized .slick-slide) {
    display: block;
  }
</style>
