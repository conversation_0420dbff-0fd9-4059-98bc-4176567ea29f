import {defineStore} from 'pinia'
import {computed, ref, watch, watchEffect} from 'vue'
import {useRouter} from 'vue-router'
import {useCookies} from 'vue3-cookies'
import {useLocale} from 'vuetify'
import {LOCAL_STORE_LABEL, PATH_NAME, defaultCookiesOptions} from '../defined/const'
import {useMessageDialogStore} from './messag-dialog'

export const useAuthStore = defineStore('auth', () => {
  const {t: translate} = useLocale()
  const {cookies} = useCookies()
  const msgStore = useMessageDialogStore()
  const router = useRouter()

  const token = ref(null)
  const nickname = ref('')
  const isAuthenticated = computed(() => !!token.value)

  watchEffect(() => {
    if (!isAuthenticated.value) {
      token.value = cookies.get(LOCAL_STORE_LABEL.SESSION_TOKEN)
    }
  })

  const showLogoutMessage = () => {
    msgStore.setShowMessage(translate('auth.logoutMessage'), {
      showLogoutButton: true,
      showTopCloseButton: false,
    })
  }

  const reloadCookies = () => {
    token.value = cookies.get(LOCAL_STORE_LABEL.SESSION_TOKEN)
    nickname.value = cookies.get(LOCAL_STORE_LABEL.SESSION_USERNAME)
  }

  const setToken = tok => {
    token.value = tok
    cookies.set(LOCAL_STORE_LABEL.SESSION_TOKEN, tok, defaultCookiesOptions)
  }

  const setNickname = name => {
    nickname.value = name || 'ニックネームなし'
    cookies.set(LOCAL_STORE_LABEL.SESSION_USERNAME, nickname.value, defaultCookiesOptions)
  }

  const handleLogout = async () => {
    cookies.remove(LOCAL_STORE_LABEL.SESSION_TOKEN)
    cookies.remove(LOCAL_STORE_LABEL.SESSION_USERNAME)
    token.value = null
    await router.replace(PATH_NAME.TOP)
    location.reload()
  }

  const onLogoutClick = () => handleLogout()

  watch(() => msgStore.clickedLogout, onLogoutClick)

  return {
    isAuthenticated,
    token,
    setToken,
    showLogoutMessage,
    handleLogout,
    reloadCookies,
    nickname,
    setNickname,
  }
})
