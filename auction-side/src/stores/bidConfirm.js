import {defineStore} from 'pinia'
import {ref} from 'vue'

export const useBidConfirmStore = defineStore('bidConfirm', () => {
  const showBidResult = ref(false)
  const showBidConfirm = ref(false)
  const agree = ref(false)
  const data = ref([
    {
      exhibitionNo: null,
      exhibitionName: '',
      bidTotalPrice: null,
      bidList: [
        {
          exhibitionItemNo: null,
          bidQuantity: null,
          bidPrice: null,
          bidTotalPrice: null,
          freeField: {
            product_name: '',
            sim: '',
            capacity: '',
            color: '',
            rank: '',
          },
        },
      ],
    },
  ])

  const setShowBidResult = val => {
    showBidResult.value = val
  }

  return {
    showBidConfirm,
    showBidResult,
    agree,
    data,
    setShowBidResult,
    // resetParams,
  }
})

export const toggleBidConfirmDialog = () => {
  const store = useBidConfirmStore()
  store.showBidConfirm = !store.showBidConfirm
}

export const bidConfirmDialogResetParams = () => {
  const store = useBidConfirmStore()

  // No need [.value] because ref was unwrap in pinia store
  store.showBidResult = false
  store.showBidConfirm = false
  store.agree = false
  store.data = []
}
